#!/usr/bin/env python3
"""
Test script to verify the quiz phase handoff fix.

This test verifies that the missing handle_quiz_and_completion_phases function
has been implemented and that the handoff from AI Instructor to backend quiz
system works correctly.
"""

import sys
import os
import asyncio
import json
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_function_exists():
    """Test that the handle_quiz_and_completion_phases function exists"""
    print("🧪 Testing function existence...")
    
    try:
        from main import handle_quiz_and_completion_phases
        print("✅ handle_quiz_and_completion_phases function found")
        return True
    except ImportError as e:
        print(f"❌ handle_quiz_and_completion_phases function not found: {e}")
        return False

async def test_handoff_logic():
    """Test the handoff logic with mock data"""
    print("\n🧪 Testing handoff logic...")
    
    try:
        from main import handle_quiz_and_completion_phases
        
        # Mock context for quiz_initiate phase
        mock_context = {
            'lesson_phase': 'quiz_initiate',
            'student_name': 'Test Student',
            'topic': 'Test Topic',
            'handoff_to_backend': True,
            'teaching_complete': True
        }
        
        # Test the function call
        response, next_phase, state_updates = await handle_quiz_and_completion_phases(
            mock_context, 
            "I'm ready for the quiz!", 
            "test_request_123"
        )
        
        print(f"✅ Function executed successfully")
        print(f"   Response: {response[:100]}...")
        print(f"   Next phase: {next_phase}")
        print(f"   State updates: {state_updates}")
        
        # Verify expected behavior
        if next_phase == 'quiz_questions':
            print("✅ Correct phase transition: quiz_initiate → quiz_questions")
            return True
        else:
            print(f"❌ Unexpected phase transition: quiz_initiate → {next_phase}")
            return False
            
    except Exception as e:
        print(f"❌ Handoff logic test failed: {e}")
        return False

async def test_quiz_questions_phase():
    """Test quiz questions phase handling"""
    print("\n🧪 Testing quiz questions phase...")
    
    try:
        from main import handle_quiz_and_completion_phases
        
        # Mock context for quiz_questions phase
        mock_context = {
            'lesson_phase': 'quiz_questions',
            'student_name': 'Test Student',
            'topic': 'Test Topic',
            'quiz_questions_generated': [
                {'question': 'Test question 1', 'type': 'multiple_choice'},
                {'question': 'Test question 2', 'type': 'multiple_choice'}
            ],
            'current_quiz_question': 0,
            'quiz_answers': []
        }
        
        # Test the function call
        response, next_phase, state_updates = await handle_quiz_and_completion_phases(
            mock_context, 
            "Answer A", 
            "test_request_456"
        )
        
        print(f"✅ Quiz questions phase executed successfully")
        print(f"   Response: {response[:100]}...")
        print(f"   Next phase: {next_phase}")
        
        return True
            
    except Exception as e:
        print(f"❌ Quiz questions phase test failed: {e}")
        return False

def test_import_dependencies():
    """Test that all required dependencies can be imported"""
    print("\n🧪 Testing import dependencies...")
    
    try:
        from main import (
            handle_quiz_initiate_phase,
            handle_quiz_questions_phase,
            handle_quiz_results_phase,
            handle_conclusion_summary_phase,
            handle_final_assessment_phase,
            get_gemini_model
        )
        print("✅ All required functions imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Quiz Handoff Fix Verification Test")
    print("=" * 50)
    
    tests = [
        ("Function Existence", test_function_exists),
        ("Import Dependencies", test_import_dependencies),
        ("Handoff Logic", test_handoff_logic),
        ("Quiz Questions Phase", test_quiz_questions_phase)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Quiz handoff fix is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
