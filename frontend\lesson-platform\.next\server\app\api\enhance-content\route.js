/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/enhance-content/route";
exports.ids = ["app/api/enhance-content/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fenhance-content%2Froute&page=%2Fapi%2Fenhance-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhance-content%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fenhance-content%2Froute&page=%2Fapi%2Fenhance-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhance-content%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_enhance_content_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/enhance-content/route.ts */ \"(rsc)/./src/app/api/enhance-content/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/enhance-content/route\",\n        pathname: \"/api/enhance-content\",\n        filename: \"route\",\n        bundlePath: \"app/api/enhance-content/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\api\\\\enhance-content\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_enhance_content_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fenhance-content%2Froute&page=%2Fapi%2Fenhance-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhance-content%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/enhance-content/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/enhance-content/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/config */ \"(rsc)/./src/lib/config.ts\");\n\n\n // Assuming you have a logger utility\n // Assuming you have a config utility for backend URL\nconst FLASK_API_ENDPOINT = '/api/enhance-content'; // The specific endpoint on your Flask backend\nasync function POST(request) {\n    const logPrefix = '[API Proxy /api/enhance-content]';\n    const frontendRequestId = request.headers.get('x-request-id') || crypto.randomUUID(); // Use incoming or generate\n    _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Request received.`);\n    let requestBodyFromClient;\n    try {\n        requestBodyFromClient = await request.json();\n        _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Parsed body from client:`, JSON.stringify(requestBodyFromClient).substring(0, 500) + \"...\");\n    } catch (e) {\n        _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Error parsing JSON body from client:`, e.message);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Invalid JSON payload.'\n        }, {\n            status: 400\n        });\n    }\n    const flaskBackendUrl = ((0,_lib_config__WEBPACK_IMPORTED_MODULE_2__.getBackendUrl)() || process.env.FLASK_BACKEND_URL || 'http://localhost:5000').replace(/\\/$/, \"\");\n    const fullFlaskUrl = `${flaskBackendUrl}${FLASK_API_ENDPOINT}`;\n    // Prepare headers to forward to Flask\n    // Crucially, forward the Authorization header for Flask's @require_auth\n    const authorizationHeader = request.headers.get('Authorization');\n    const studentIdHeader = request.headers.get('X-Student-Id'); // If you use this custom header\n    const headersToFlask = {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n    };\n    if (authorizationHeader) {\n        headersToFlask['Authorization'] = authorizationHeader;\n    }\n    // Add any other headers you need to pass, like X-Student-Id if Flask uses it\n    // For example, if your Flask's @require_auth gets student ID from a custom header:\n    // if (requestBodyFromClient.student_id) { // Or get from JWT if preferred on Flask side\n    //     headersToFlask['X-Student-Id'] = requestBodyFromClient.student_id;\n    // }\n    _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Forwarding request to Flask: ${fullFlaskUrl}`);\n    _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.debug(`${logPrefix} [${frontendRequestId}] Body to Flask:`, requestBodyFromClient);\n    _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.debug(`${logPrefix} [${frontendRequestId}] Headers to Flask:`, headersToFlask);\n    // Determine timeout – allow override via env var, otherwise default to 3 minutes\n    const timeoutMs = Number(process.env.AI_CONTENT_TIMEOUT_MS || 180000);\n    try {\n        const flaskResponse = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(fullFlaskUrl, requestBodyFromClient, {\n            headers: headersToFlask,\n            timeout: timeoutMs\n        });\n        _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Response from Flask. Status: ${flaskResponse.status}.`);\n        // Enhanced logging for debugging\n        _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Type of flaskResponse.data: ${typeof flaskResponse.data}`);\n        try {\n            const dataString = JSON.stringify(flaskResponse.data);\n            const dataPreview = dataString.substring(0, 1000);\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] flaskResponse.data (preview): ${dataPreview}${dataString.length > 1000 ? '...' : ''}`);\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Response size: ${dataString.length} bytes`);\n        } catch (e) {\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Could not stringify flaskResponse.data. Raw:`, flaskResponse.data);\n        }\n        if (flaskResponse.status >= 200 && flaskResponse.status < 300 && flaskResponse.data) {\n            const responseKeys = typeof flaskResponse.data === 'object' && flaskResponse.data !== null ? Object.keys(flaskResponse.data).join(', ') : 'non-object response';\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Forwarding FULL Flask data to client. Response keys: ${responseKeys}`);\n            // Forward Flask's response directly to the client\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(flaskResponse.data, {\n                status: flaskResponse.status\n            });\n        } else {\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Invalid response from Flask:`, {\n                status: flaskResponse.status,\n                hasData: !!flaskResponse.data,\n                dataType: typeof flaskResponse.data\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Invalid response from backend',\n                status: flaskResponse.status\n            }, {\n                status: 502\n            });\n        }\n    } catch (error) {\n        _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Error proxying to Flask:`, error.message);\n        if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(error)) {\n            const axiosError = error; // Type assertion\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Axios error details: Code: ${axiosError.code}, Status: ${axiosError.response?.status}`);\n            // logger.error(`${logPrefix} [${frontendRequestId}] Axios error response data:`, axiosError.response?.data);\n            const statusCode = axiosError.response?.status || 502; // 502 Bad Gateway if backend error\n            const flaskErrorMessage = axiosError.response?.data?.message || axiosError.message || 'Backend communication error.';\n            const flaskRequestId = axiosError.response?.data?.request_id || null;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: `Backend communication error: ${flaskErrorMessage}`,\n                details: axiosError.response?.data || null,\n                backendRequestId: flaskRequestId\n            }, {\n                status: statusCode\n            });\n        }\n        // For non-Axios errors during proxying\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Internal proxy error while communicating with backend.'\n        }, {\n            status: 500\n        });\n    }\n}\n// Optional: Add an OPTIONS handler for CORS preflight if needed\n// This might be necessary if your global CORS config in next.config.js isn't sufficient\n// or if you have specific header requirements for this route.\nasync function OPTIONS(request) {\n    const logPrefix = '[API Proxy /api/enhance-content]';\n    _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} OPTIONS request received.`);\n    const responseHeaders = new Headers();\n    // Match these with what your Flask backend's CORS setup allows for the actual request\n    responseHeaders.set('Access-Control-Allow-Origin', request.headers.get('Origin') || '*'); // Or your specific frontend origin\n    responseHeaders.set('Access-Control-Allow-Methods', 'POST, OPTIONS');\n    responseHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Student-Id, X-Request-Id'); // Add any custom headers\n    responseHeaders.set('Access-Control-Allow-Credentials', 'true');\n    responseHeaders.set('Access-Control-Max-Age', '86400'); // Cache preflight for 1 day\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 204,\n        headers: responseHeaders\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/enhance-content/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/env.ts":
/*!********************!*\
  !*** ./src/env.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   env: () => (/* binding */ env),\n/* harmony export */   publicEnv: () => (/* binding */ publicEnv)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n// File: env.ts\n\n/**\r\n * Environment variable schema definition\r\n * Add all environment variables your application uses here\r\n */ const baseSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    // Environment\n    NODE_ENV: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'development',\n        'production',\n        'test'\n    ]).default('development'),\n    // Logging\n    LOG_LEVEL: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'debug',\n        'info',\n        'warn',\n        'error'\n    ]).default('info'),\n    PRETTY_PRINT: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'true',\n        'false'\n    ]).default('false').transform((val)=>val === 'true'),\n    SERVICE_NAME: zod__WEBPACK_IMPORTED_MODULE_0__.string().default('education-platform'),\n    // Public API URL\n    NEXT_PUBLIC_API_URL: zod__WEBPACK_IMPORTED_MODULE_0__.string().url().optional(),\n    // Public Firebase Config (Client-side) - Renamed to match convention\n    NEXT_PUBLIC_FIREBASE_API_KEY: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    NEXT_PUBLIC_FIREBASE_PROJECT_ID: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    NEXT_PUBLIC_FIREBASE_APP_ID: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    // Feature flags (can be public)\n    NEXT_PUBLIC_ENABLE_TIMETABLE_GENERATION: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'true',\n        'false'\n    ]).default('true').transform((val)=>val === 'true'),\n    NEXT_PUBLIC_ENABLE_ENROLLMENT_API: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'true',\n        'false'\n    ]).default('true').transform((val)=>val === 'true')\n});\n// Server-side specific schema, extending the base\nconst serverSchema = baseSchema.extend({\n    // Firebase Admin (Server-side) - Kept original names as they are not public\n    FIREBASE_PROJECT_ID: zod__WEBPACK_IMPORTED_MODULE_0__.string(),\n    FIREBASE_CLIENT_EMAIL: zod__WEBPACK_IMPORTED_MODULE_0__.string().email(),\n    FIREBASE_PRIVATE_KEY: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(10)\n});\n// Client-side schema only includes the base (public) variables\nconst clientSchema = baseSchema;\n/**\r\n * Parse and validate environment variables\r\n * This will throw an error if required environment variables are missing\r\n */ function createEnv() {\n    const isServer = typeof process !== 'undefined' && process.env;\n    const isBrowser = \"undefined\" !== 'undefined';\n    let envSource = {};\n    let schemaToUse;\n    if (isServer) {\n        console.log('[env.ts] Running on server, using process.env');\n        envSource = process.env;\n        schemaToUse = serverSchema;\n    } else if (isBrowser) {\n        // console.log('[env.ts] Running in browser');\n        // Construct the object for Zod validation using only available client-side variables\n        const browserEnv = {\n            NODE_ENV: \"development\",\n            // Add public vars from process.env (replaced by Next.js build)\n            NEXT_PUBLIC_API_URL: \"http://localhost:5000\",\n            NEXT_PUBLIC_FIREBASE_API_KEY: \"AIzaSyDWVM8PvcWD4nAkpsI7FuDKCvpp_PEnPlU\",\n            NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: \"solynta-academy.firebaseapp.com\",\n            NEXT_PUBLIC_FIREBASE_PROJECT_ID: \"solynta-academy\",\n            NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: \"solynta-academy.firebasestorage.app\",\n            NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: \"914922463191\",\n            NEXT_PUBLIC_FIREBASE_APP_ID: \"1:914922463191:web:b6e9c737dba77a26643592\",\n            NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID: \"G-ZVC7R06Y33\",\n            NEXT_PUBLIC_ENABLE_TIMETABLE_GENERATION: process.env.NEXT_PUBLIC_ENABLE_TIMETABLE_GENERATION,\n            NEXT_PUBLIC_ENABLE_ENROLLMENT_API: process.env.NEXT_PUBLIC_ENABLE_ENROLLMENT_API\n        };\n        // Filter out undefined values before parsing.\n        // Zod handles missing keys better than explicit undefined for defaults.\n        envSource = Object.entries(browserEnv).reduce((acc, [key, value])=>{\n            if (value !== undefined) {\n                acc[key] = value;\n            }\n            return acc;\n        }, {});\n        // Use clientSchema (baseSchema) for validation\n        schemaToUse = clientSchema;\n    } else {\n        console.warn('[env.ts] Environment context unclear (neither server nor browser). Using empty source.');\n        // Fallback or throw error depending on requirements\n        return clientSchema.parse({}); // Parse against client schema with empty source (will use defaults)\n    }\n    // Log the source keys being parsed (optional debugging)\n    // console.log(`[env.ts] Parsing env source keys: ${Object.keys(envSource).join(', ')} using ${isServer ? 'server' : 'client'} schema`);\n    try {\n        // Use safeParse to avoid throwing immediately, allowing for better error reporting\n        const parsed = schemaToUse.safeParse(envSource);\n        if (!parsed.success) {\n            console.error('❌ Invalid environment variables (raw error):', parsed.error // Log the raw error object\n            );\n            console.error('❌ Invalid environment variables (formatted):', // Use format() for detailed error messages\n            parsed.error.format());\n            // In development/test, return partial data with defaults to allow app to run partially\n            if (envSource.NODE_ENV !== 'production') {\n                console.warn('[env.ts] Parsing failed, returning partial env with defaults for non-production.');\n                // Attempt to parse with partial schema - might still fail if types are wrong\n                const partialParsed = schemaToUse.partial().safeParse(envSource);\n                if (partialParsed.success) {\n                    return partialParsed.data;\n                } else {\n                    console.error('[env.ts] Partial parsing also failed:', partialParsed.error.format());\n                    // Return minimal defaults if even partial fails\n                    return clientSchema.parse({}); // Return base defaults\n                }\n            }\n            throw new Error('Invalid environment variables'); // Throw in production\n        }\n        // console.log('[env.ts] Environment variables parsed successfully.');\n        return parsed.data;\n    } catch (error) {\n        console.error('[env.ts] Critical error during environment variable parsing:', error);\n        // Decide how to handle critical failure, e.g., throw or return defaults\n        if (true) {\n            console.warn('[env.ts] Returning default environment due to critical parsing error.');\n            return clientSchema.parse({}); // Return base defaults in non-prod\n        }\n        throw new Error('Failed to parse environment variables.');\n    }\n}\n// Export validated environment\nconst env = createEnv();\n// Create safe versions for client-side use - ensure keys match the clientSchema/baseSchema\nconst publicEnv = {\n    NODE_ENV: env.NODE_ENV,\n    NEXT_PUBLIC_API_URL: env.NEXT_PUBLIC_API_URL,\n    NEXT_PUBLIC_FIREBASE_API_KEY: env.NEXT_PUBLIC_FIREBASE_API_KEY,\n    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n    NEXT_PUBLIC_FIREBASE_PROJECT_ID: env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n    NEXT_PUBLIC_FIREBASE_APP_ID: env.NEXT_PUBLIC_FIREBASE_APP_ID,\n    NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID: env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,\n    NEXT_PUBLIC_ENABLE_TIMETABLE_GENERATION: env.NEXT_PUBLIC_ENABLE_TIMETABLE_GENERATION,\n    NEXT_PUBLIC_ENABLE_ENROLLMENT_API: env.NEXT_PUBLIC_ENABLE_ENROLLMENT_API\n};\n // Use clientSchema for public type\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/env.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAiInstructorUrl: () => (/* binding */ getAiInstructorUrl),\n/* harmony export */   getAiTutorUrl: () => (/* binding */ getAiTutorUrl),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getBackendUrl: () => (/* binding */ getBackendUrl),\n/* harmony export */   getEnvironment: () => (/* binding */ getEnvironment),\n/* harmony export */   getFrontendUrl: () => (/* binding */ getFrontendUrl),\n/* harmony export */   isDevelopment: () => (/* binding */ isDevelopment),\n/* harmony export */   isProduction: () => (/* binding */ isProduction),\n/* harmony export */   isTest: () => (/* binding */ isTest)\n/* harmony export */ });\n/**\r\n * Configuration utilities for the application\r\n */ /**\r\n * Get the backend URL based on the current environment\r\n * @returns The backend URL\r\n */ function getBackendUrl() {\n    // Use environment variable or default to empty string (same origin)\n    // Try multiple environment variable names for backwards compatibility\n    const backendUrl = \"http://localhost:5000\" || 0 || 0;\n    // For development, provide a fallback localhost URL if no backend URL is specified\n    if (!backendUrl && \"development\" === 'development') {\n        return 'http://localhost:5000';\n    }\n    // Validate URL format if one is provided\n    if (backendUrl && !backendUrl.startsWith('http')) {\n        console.warn('Backend URL should start with http:// or https://');\n    }\n    return backendUrl;\n}\n/**\r\n * Get the frontend URL based on the current environment\r\n * @returns The frontend URL\r\n */ function getFrontendUrl() {\n    return process.env.NEXT_PUBLIC_FRONTEND_URL || ( false ? 0 : '');\n}\n/**\r\n * Get the full URL for a specific API endpoint\r\n * @param endpoint The API endpoint path\r\n * @returns The complete API URL\r\n */ function getApiUrl(endpoint) {\n    const baseUrl = getBackendUrl();\n    // If there's no backend URL, use same-origin API endpoint\n    if (!baseUrl) {\n        // If endpoint doesn't start with a slash, add it\n        return endpoint.startsWith('/') ? endpoint : `/${endpoint}`;\n    }\n    // Ensure there's a single slash between base URL and endpoint\n    const normalizedBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n    const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;\n    return `${normalizedBaseUrl}${normalizedEndpoint}`;\n}\n/**\r\n * Get the URL for AI instructor API\r\n * @returns The AI instructor API URL\r\n */ function getAiInstructorUrl() {\n    return getApiUrl('/ai-instructor');\n}\n/**\r\n * Get the URL for AI tutor API\r\n * @returns The AI tutor API URL\r\n */ function getAiTutorUrl() {\n    return getApiUrl('/ai-tutor');\n}\n/**\r\n * Get the current environment name\r\n * @returns The environment name (development, production, test)\r\n */ function getEnvironment() {\n    return \"development\" || 0;\n}\n/**\r\n * Check if the application is running in development mode\r\n * @returns True if in development mode\r\n */ function isDevelopment() {\n    return getEnvironment() === 'development';\n}\n/**\r\n * Check if the application is running in production mode\r\n * @returns True if in production mode\r\n */ function isProduction() {\n    return getEnvironment() === 'production';\n}\n/**\r\n * Check if the application is running in test mode\r\n * @returns True if in test mode\r\n */ function isTest() {\n    return getEnvironment() === 'test';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2NvbmZpZy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQTs7Q0FFQyxHQUVEOzs7Q0FHQyxHQUNNLFNBQVNBO0lBQ2Qsb0VBQW9FO0lBQ3BFLHNFQUFzRTtJQUN0RSxNQUFNQyxhQUFhQyx1QkFBbUMsSUFDcENBLENBQStCLElBQy9CLENBQUU7SUFFcEIsbUZBQW1GO0lBQ25GLElBQUksQ0FBQ0QsY0FBY0Msa0JBQXlCLGVBQWU7UUFDekQsT0FBTztJQUNUO0lBRUEseUNBQXlDO0lBQ3pDLElBQUlELGNBQWMsQ0FBQ0EsV0FBV0ssVUFBVSxDQUFDLFNBQVM7UUFDaERDLFFBQVFDLElBQUksQ0FBQztJQUNmO0lBRUEsT0FBT1A7QUFDVDtBQUVBOzs7Q0FHQyxHQUNNLFNBQVNRO0lBQ2QsT0FBT1AsUUFBUUMsR0FBRyxDQUFDTyx3QkFBd0IsSUFDeEMsT0FBNkIsR0FBR0MsQ0FBc0IsR0FBRyxFQUFDO0FBQy9EO0FBRUE7Ozs7Q0FJQyxHQUNNLFNBQVNHLFVBQVVDLFFBQWdCO0lBQ3hDLE1BQU1DLFVBQVVoQjtJQUVoQiwwREFBMEQ7SUFDMUQsSUFBSSxDQUFDZ0IsU0FBUztRQUNaLGlEQUFpRDtRQUNqRCxPQUFPRCxTQUFTVCxVQUFVLENBQUMsT0FBT1MsV0FBVyxDQUFDLENBQUMsRUFBRUEsVUFBVTtJQUM3RDtJQUVBLDhEQUE4RDtJQUM5RCxNQUFNRSxvQkFBb0JELFFBQVFFLFFBQVEsQ0FBQyxPQUFPRixRQUFRRyxLQUFLLENBQUMsR0FBRyxDQUFDLEtBQUtIO0lBQ3pFLE1BQU1JLHFCQUFxQkwsU0FBU1QsVUFBVSxDQUFDLE9BQU9TLFdBQVcsQ0FBQyxDQUFDLEVBQUVBLFVBQVU7SUFFL0UsT0FBTyxHQUFHRSxvQkFBb0JHLG9CQUFvQjtBQUNwRDtBQUVBOzs7Q0FHQyxHQUNNLFNBQVNDO0lBQ2QsT0FBT1AsVUFBVTtBQUNuQjtBQUVBOzs7Q0FHQyxHQUNNLFNBQVNRO0lBQ2QsT0FBT1IsVUFBVTtBQUNuQjtBQUVBOzs7Q0FHQyxHQUNNLFNBQVNTO0lBQ2QsT0FBT3JCLGlCQUF3QixDQUFhO0FBQzlDO0FBRUE7OztDQUdDLEdBQ00sU0FBU3NCO0lBQ2QsT0FBT0QscUJBQXFCO0FBQzlCO0FBRUE7OztDQUdDLEdBQ00sU0FBU0U7SUFDZCxPQUFPRixxQkFBcUI7QUFDOUI7QUFFQTs7O0NBR0MsR0FDTSxTQUFTRztJQUNkLE9BQU9ILHFCQUFxQjtBQUM5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcc3JjXFxsaWJcXGNvbmZpZy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcclxuICogQ29uZmlndXJhdGlvbiB1dGlsaXRpZXMgZm9yIHRoZSBhcHBsaWNhdGlvblxyXG4gKi9cclxuXHJcbi8qKlxyXG4gKiBHZXQgdGhlIGJhY2tlbmQgVVJMIGJhc2VkIG9uIHRoZSBjdXJyZW50IGVudmlyb25tZW50XHJcbiAqIEByZXR1cm5zIFRoZSBiYWNrZW5kIFVSTFxyXG4gKi9cclxuZXhwb3J0IGZ1bmN0aW9uIGdldEJhY2tlbmRVcmwoKTogc3RyaW5nIHtcclxuICAvLyBVc2UgZW52aXJvbm1lbnQgdmFyaWFibGUgb3IgZGVmYXVsdCB0byBlbXB0eSBzdHJpbmcgKHNhbWUgb3JpZ2luKVxyXG4gIC8vIFRyeSBtdWx0aXBsZSBlbnZpcm9ubWVudCB2YXJpYWJsZSBuYW1lcyBmb3IgYmFja3dhcmRzIGNvbXBhdGliaWxpdHlcclxuICBjb25zdCBiYWNrZW5kVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQkFDS0VORF9VUkwgfHwgXHJcbiAgICAgICAgICAgICAgICAgICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCBcclxuICAgICAgICAgICAgICAgICAgICAnJztcclxuICBcclxuICAvLyBGb3IgZGV2ZWxvcG1lbnQsIHByb3ZpZGUgYSBmYWxsYmFjayBsb2NhbGhvc3QgVVJMIGlmIG5vIGJhY2tlbmQgVVJMIGlzIHNwZWNpZmllZFxyXG4gIGlmICghYmFja2VuZFVybCAmJiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50Jykge1xyXG4gICAgcmV0dXJuICdodHRwOi8vbG9jYWxob3N0OjUwMDAnO1xyXG4gIH1cclxuICBcclxuICAvLyBWYWxpZGF0ZSBVUkwgZm9ybWF0IGlmIG9uZSBpcyBwcm92aWRlZFxyXG4gIGlmIChiYWNrZW5kVXJsICYmICFiYWNrZW5kVXJsLnN0YXJ0c1dpdGgoJ2h0dHAnKSkge1xyXG4gICAgY29uc29sZS53YXJuKCdCYWNrZW5kIFVSTCBzaG91bGQgc3RhcnQgd2l0aCBodHRwOi8vIG9yIGh0dHBzOi8vJyk7XHJcbiAgfVxyXG4gIFxyXG4gIHJldHVybiBiYWNrZW5kVXJsO1xyXG59XHJcblxyXG4vKipcclxuICogR2V0IHRoZSBmcm9udGVuZCBVUkwgYmFzZWQgb24gdGhlIGN1cnJlbnQgZW52aXJvbm1lbnRcclxuICogQHJldHVybnMgVGhlIGZyb250ZW5kIFVSTFxyXG4gKi9cclxuZXhwb3J0IGZ1bmN0aW9uIGdldEZyb250ZW5kVXJsKCk6IHN0cmluZyB7XHJcbiAgcmV0dXJuIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0ZST05URU5EX1VSTCB8fCBcclxuICAgICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4gOiAnJyk7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBHZXQgdGhlIGZ1bGwgVVJMIGZvciBhIHNwZWNpZmljIEFQSSBlbmRwb2ludFxyXG4gKiBAcGFyYW0gZW5kcG9pbnQgVGhlIEFQSSBlbmRwb2ludCBwYXRoXHJcbiAqIEByZXR1cm5zIFRoZSBjb21wbGV0ZSBBUEkgVVJMXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gZ2V0QXBpVXJsKGVuZHBvaW50OiBzdHJpbmcpOiBzdHJpbmcge1xyXG4gIGNvbnN0IGJhc2VVcmwgPSBnZXRCYWNrZW5kVXJsKCk7XHJcbiAgXHJcbiAgLy8gSWYgdGhlcmUncyBubyBiYWNrZW5kIFVSTCwgdXNlIHNhbWUtb3JpZ2luIEFQSSBlbmRwb2ludFxyXG4gIGlmICghYmFzZVVybCkge1xyXG4gICAgLy8gSWYgZW5kcG9pbnQgZG9lc24ndCBzdGFydCB3aXRoIGEgc2xhc2gsIGFkZCBpdFxyXG4gICAgcmV0dXJuIGVuZHBvaW50LnN0YXJ0c1dpdGgoJy8nKSA/IGVuZHBvaW50IDogYC8ke2VuZHBvaW50fWA7XHJcbiAgfVxyXG4gIFxyXG4gIC8vIEVuc3VyZSB0aGVyZSdzIGEgc2luZ2xlIHNsYXNoIGJldHdlZW4gYmFzZSBVUkwgYW5kIGVuZHBvaW50XHJcbiAgY29uc3Qgbm9ybWFsaXplZEJhc2VVcmwgPSBiYXNlVXJsLmVuZHNXaXRoKCcvJykgPyBiYXNlVXJsLnNsaWNlKDAsIC0xKSA6IGJhc2VVcmw7XHJcbiAgY29uc3Qgbm9ybWFsaXplZEVuZHBvaW50ID0gZW5kcG9pbnQuc3RhcnRzV2l0aCgnLycpID8gZW5kcG9pbnQgOiBgLyR7ZW5kcG9pbnR9YDtcclxuICBcclxuICByZXR1cm4gYCR7bm9ybWFsaXplZEJhc2VVcmx9JHtub3JtYWxpemVkRW5kcG9pbnR9YDtcclxufVxyXG5cclxuLyoqXHJcbiAqIEdldCB0aGUgVVJMIGZvciBBSSBpbnN0cnVjdG9yIEFQSVxyXG4gKiBAcmV0dXJucyBUaGUgQUkgaW5zdHJ1Y3RvciBBUEkgVVJMXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gZ2V0QWlJbnN0cnVjdG9yVXJsKCk6IHN0cmluZyB7XHJcbiAgcmV0dXJuIGdldEFwaVVybCgnL2FpLWluc3RydWN0b3InKTtcclxufVxyXG5cclxuLyoqXHJcbiAqIEdldCB0aGUgVVJMIGZvciBBSSB0dXRvciBBUElcclxuICogQHJldHVybnMgVGhlIEFJIHR1dG9yIEFQSSBVUkxcclxuICovXHJcbmV4cG9ydCBmdW5jdGlvbiBnZXRBaVR1dG9yVXJsKCk6IHN0cmluZyB7XHJcbiAgcmV0dXJuIGdldEFwaVVybCgnL2FpLXR1dG9yJyk7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBHZXQgdGhlIGN1cnJlbnQgZW52aXJvbm1lbnQgbmFtZVxyXG4gKiBAcmV0dXJucyBUaGUgZW52aXJvbm1lbnQgbmFtZSAoZGV2ZWxvcG1lbnQsIHByb2R1Y3Rpb24sIHRlc3QpXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gZ2V0RW52aXJvbm1lbnQoKTogc3RyaW5nIHtcclxuICByZXR1cm4gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgfHwgJ2RldmVsb3BtZW50JztcclxufVxyXG5cclxuLyoqXHJcbiAqIENoZWNrIGlmIHRoZSBhcHBsaWNhdGlvbiBpcyBydW5uaW5nIGluIGRldmVsb3BtZW50IG1vZGVcclxuICogQHJldHVybnMgVHJ1ZSBpZiBpbiBkZXZlbG9wbWVudCBtb2RlXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gaXNEZXZlbG9wbWVudCgpOiBib29sZWFuIHtcclxuICByZXR1cm4gZ2V0RW52aXJvbm1lbnQoKSA9PT0gJ2RldmVsb3BtZW50JztcclxufVxyXG5cclxuLyoqXHJcbiAqIENoZWNrIGlmIHRoZSBhcHBsaWNhdGlvbiBpcyBydW5uaW5nIGluIHByb2R1Y3Rpb24gbW9kZVxyXG4gKiBAcmV0dXJucyBUcnVlIGlmIGluIHByb2R1Y3Rpb24gbW9kZVxyXG4gKi9cclxuZXhwb3J0IGZ1bmN0aW9uIGlzUHJvZHVjdGlvbigpOiBib29sZWFuIHtcclxuICByZXR1cm4gZ2V0RW52aXJvbm1lbnQoKSA9PT0gJ3Byb2R1Y3Rpb24nO1xyXG59XHJcblxyXG4vKipcclxuICogQ2hlY2sgaWYgdGhlIGFwcGxpY2F0aW9uIGlzIHJ1bm5pbmcgaW4gdGVzdCBtb2RlXHJcbiAqIEByZXR1cm5zIFRydWUgaWYgaW4gdGVzdCBtb2RlXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gaXNUZXN0KCk6IGJvb2xlYW4ge1xyXG4gIHJldHVybiBnZXRFbnZpcm9ubWVudCgpID09PSAndGVzdCc7XHJcbn0iXSwibmFtZXMiOlsiZ2V0QmFja2VuZFVybCIsImJhY2tlbmRVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQkFDS0VORF9VUkwiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwic3RhcnRzV2l0aCIsImNvbnNvbGUiLCJ3YXJuIiwiZ2V0RnJvbnRlbmRVcmwiLCJORVhUX1BVQkxJQ19GUk9OVEVORF9VUkwiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsIm9yaWdpbiIsImdldEFwaVVybCIsImVuZHBvaW50IiwiYmFzZVVybCIsIm5vcm1hbGl6ZWRCYXNlVXJsIiwiZW5kc1dpdGgiLCJzbGljZSIsIm5vcm1hbGl6ZWRFbmRwb2ludCIsImdldEFpSW5zdHJ1Y3RvclVybCIsImdldEFpVHV0b3JVcmwiLCJnZXRFbnZpcm9ubWVudCIsImlzRGV2ZWxvcG1lbnQiLCJpc1Byb2R1Y3Rpb24iLCJpc1Rlc3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/logger.ts":
/*!***************************!*\
  !*** ./src/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\n/* harmony import */ var _env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/env */ \"(rsc)/./src/env.ts\");\n// File: lib/logger.ts\n/**\r\n * Application logger that provides consistent logging across the application\r\n * Can be configured to output to different destinations based on environment\r\n */ // Import environment variables\n\n// Environment-aware log level\nconst LOG_LEVEL = _env__WEBPACK_IMPORTED_MODULE_0__.env.LOG_LEVEL || 'info';\n// Numeric log level priorities (higher = more severe)\nconst LOG_LEVEL_PRIORITY = {\n    debug: 0,\n    info: 1,\n    warn: 2,\n    error: 3\n};\n// Current environment\nconst NODE_ENV = _env__WEBPACK_IMPORTED_MODULE_0__.env.NODE_ENV || 'development';\n// Should we pretty print logs?\nconst PRETTY_PRINT = _env__WEBPACK_IMPORTED_MODULE_0__.env.PRETTY_PRINT === 'true' || NODE_ENV === 'development';\n/**\r\n * Determine if a log at the given level should be output\r\n * based on the configured minimum log level\r\n */ function shouldLog(level) {\n    return LOG_LEVEL_PRIORITY[level] >= LOG_LEVEL_PRIORITY[LOG_LEVEL];\n}\n/**\r\n * Format a log message based on environment and settings\r\n */ function formatLogMessage(level, message, meta) {\n    const timestamp = new Date().toISOString();\n    if (PRETTY_PRINT) {\n        // Pretty format for development or when enabled\n        const colorCode = {\n            debug: '\\x1b[34m',\n            info: '\\x1b[32m',\n            warn: '\\x1b[33m',\n            error: '\\x1b[31m' // Red\n        }[level];\n        const reset = '\\x1b[0m';\n        const metaStr = meta ? `\\n${JSON.stringify(meta, null, 2)}` : '';\n        return `${colorCode}[${timestamp}] [${level.toUpperCase()}]${reset} ${message}${metaStr}`;\n    } else {\n        // JSON format for production or when pretty print is disabled\n        return JSON.stringify({\n            timestamp,\n            level,\n            message,\n            ...meta,\n            service: _env__WEBPACK_IMPORTED_MODULE_0__.env.SERVICE_NAME || 'app'\n        });\n    }\n}\n/**\r\n * Log a message at the specified level\r\n */ function logMessage(level, message, meta) {\n    if (!shouldLog(level)) return;\n    const formattedMessage = formatLogMessage(level, message, meta);\n    switch(level){\n        case 'debug':\n            console.debug(formattedMessage);\n            break;\n        case 'info':\n            console.info(formattedMessage);\n            break;\n        case 'warn':\n            console.warn(formattedMessage);\n            break;\n        case 'error':\n            console.error(formattedMessage);\n            break;\n    }\n}\n/**\r\n * Logger interface that can be imported throughout the application\r\n */ const logger = {\n    debug: (message, meta)=>logMessage('debug', message, meta),\n    info: (message, meta)=>logMessage('info', message, meta),\n    warn: (message, meta)=>logMessage('warn', message, meta),\n    error: (message, meta)=>logMessage('error', message, meta),\n    // Create a child logger with context\n    child: (context)=>({\n            debug: (message, meta)=>logMessage('debug', message, {\n                    ...context,\n                    ...meta\n                }),\n            info: (message, meta)=>logMessage('info', message, {\n                    ...context,\n                    ...meta\n                }),\n            warn: (message, meta)=>logMessage('warn', message, {\n                    ...context,\n                    ...meta\n                }),\n            error: (message, meta)=>logMessage('error', message, {\n                    ...context,\n                    ...meta\n                })\n        })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/logger.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@opentelemetry/api":
/*!*************************************!*\
  !*** external "@opentelemetry/api" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@opentelemetry/api");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/debug","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fenhance-content%2Froute&page=%2Fapi%2Fenhance-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhance-content%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();