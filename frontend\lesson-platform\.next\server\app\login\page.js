/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/loading.tsx */ \"(rsc)/./src/app/login/loading.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'loading': [module4, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\loading.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZsb2dpbiUyRnBhZ2UmcGFnZT0lMkZsb2dpbiUyRnBhZ2UmYXBwUGF0aHM9JTJGbG9naW4lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGbG9naW4lMkZwYWdlLnRzeCZhcHBEaXI9QyUzQSU1Q1VzZXJzJTVDcGMlNUNPbmVEcml2ZSU1Q0Rlc2t0b3AlNUNEZXNrdG9wJTVDU29seW50YV9XZWJzaXRlJTVDZnJvbnRlbmQlNUNsZXNzb24tcGxhdGZvcm0lNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q3BjJTVDT25lRHJpdmUlNUNEZXNrdG9wJTVDRGVza3RvcCU1Q1NvbHludGFfV2Vic2l0ZSU1Q2Zyb250ZW5kJTVDbGVzc29uLXBsYXRmb3JtJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxzQkFBc0Isb0pBQWdKO0FBQ3RLLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsZ09BQW1GO0FBQ3pHLHNCQUFzQixrS0FBd0o7QUFDOUssb0JBQW9CLDRKQUFxSjtBQUd2SztBQUdBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzZkFBd1M7QUFDNVU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLHNmQUF3UztBQUM1VTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUdyQjtBQUNGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUdFO0FBQ0Y7QUFDTyx3QkFBd0IsdUdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG1vZHVsZTAgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHBjXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcRGVza3RvcFxcXFxTb2x5bnRhX1dlYnNpdGVcXFxcZnJvbnRlbmRcXFxcbGVzc29uLXBsYXRmb3JtXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbmNvbnN0IG1vZHVsZTEgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIik7XG5jb25zdCBtb2R1bGUyID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMyA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTQgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHBjXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcRGVza3RvcFxcXFxTb2x5bnRhX1dlYnNpdGVcXFxcZnJvbnRlbmRcXFxcbGVzc29uLXBsYXRmb3JtXFxcXHNyY1xcXFxhcHBcXFxcbG9naW5cXFxcbG9hZGluZy50c3hcIik7XG5jb25zdCBwYWdlNSA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcc3JjXFxcXGFwcFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKTtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc3NyJ1xufTtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnbG9naW4nLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbcGFnZTUsIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcc3JjXFxcXGFwcFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xvYWRpbmcnOiBbbW9kdWxlNCwgXCJDOlxcXFxVc2Vyc1xcXFxwY1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERlc2t0b3BcXFxcU29seW50YV9XZWJzaXRlXFxcXGZyb250ZW5kXFxcXGxlc3Nvbi1wbGF0Zm9ybVxcXFxzcmNcXFxcYXBwXFxcXGxvZ2luXFxcXGxvYWRpbmcudHN4XCJdLFxuICAgICAgICBtZXRhZGF0YToge1xuICAgIGljb246IFsoYXN5bmMgKHByb3BzKSA9PiAoYXdhaXQgaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlcj90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFttb2R1bGUwLCBcIkM6XFxcXFVzZXJzXFxcXHBjXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcRGVza3RvcFxcXFxTb2x5bnRhX1dlYnNpdGVcXFxcZnJvbnRlbmRcXFxcbGVzc29uLXBsYXRmb3JtXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbbW9kdWxlMSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuJ2ZvcmJpZGRlbic6IFttb2R1bGUyLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIl0sXG4ndW5hdXRob3JpemVkJzogW21vZHVsZTMsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiXSxcbiAgICAgICAgbWV0YWRhdGE6IHtcbiAgICBpY29uOiBbKGFzeW5jIChwcm9wcykgPT4gKGF3YWl0IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXI/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIUM6XFxcXFVzZXJzXFxcXHBjXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcRGVza3RvcFxcXFxTb2x5bnRhX1dlYnNpdGVcXFxcZnJvbnRlbmRcXFxcbGVzc29uLXBsYXRmb3JtXFxcXHNyY1xcXFxhcHBcXFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX19cIikpLmRlZmF1bHQocHJvcHMpKV0sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IHVuZGVmaW5lZFxuICB9XG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJDOlxcXFxVc2Vyc1xcXFxwY1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERlc2t0b3BcXFxcU29seW50YV9XZWJzaXRlXFxcXGZyb250ZW5kXFxcXGxlc3Nvbi1wbGF0Zm9ybVxcXFxzcmNcXFxcYXBwXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9sb2dpbi9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9sb2dpblwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJycsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3BjJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDRGVza3RvcCU1QyU1Q1NvbHludGFfV2Vic2l0ZSU1QyU1Q2Zyb250ZW5kJTVDJTVDbGVzc29uLXBsYXRmb3JtJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcGMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNEZXNrdG9wJTVDJTVDU29seW50YV9XZWJzaXRlJTVDJTVDZnJvbnRlbmQlNUMlNUNsZXNzb24tcGxhdGZvcm0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtc2VnbWVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNwYyU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q0Rlc2t0b3AlNUMlNUNTb2x5bnRhX1dlYnNpdGUlNUMlNUNmcm9udGVuZCU1QyU1Q2xlc3Nvbi1wbGF0Zm9ybSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3BjJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDRGVza3RvcCU1QyU1Q1NvbHludGFfV2Vic2l0ZSU1QyU1Q2Zyb250ZW5kJTVDJTVDbGVzc29uLXBsYXRmb3JtJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDaHR0cC1hY2Nlc3MtZmFsbGJhY2slNUMlNUNlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNwYyU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q0Rlc2t0b3AlNUMlNUNTb2x5bnRhX1dlYnNpdGUlNUMlNUNmcm9udGVuZCU1QyU1Q2xlc3Nvbi1wbGF0Zm9ybSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcGMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNEZXNrdG9wJTVDJTVDU29seW50YV9XZWJzaXRlJTVDJTVDZnJvbnRlbmQlNUMlNUNsZXNzb24tcGxhdGZvcm0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3BjJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDRGVza3RvcCU1QyU1Q1NvbHludGFfV2Vic2l0ZSU1QyU1Q2Zyb250ZW5kJTVDJTVDbGVzc29uLXBsYXRmb3JtJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbWV0YWRhdGElNUMlNUNtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNwYyU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q0Rlc2t0b3AlNUMlNUNTb2x5bnRhX1dlYnNpdGUlNUMlNUNmcm9udGVuZCU1QyU1Q2xlc3Nvbi1wbGF0Zm9ybSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUF3TDtBQUN4TDtBQUNBLDBPQUEyTDtBQUMzTDtBQUNBLDBPQUEyTDtBQUMzTDtBQUNBLG9SQUFpTjtBQUNqTjtBQUNBLHdPQUEwTDtBQUMxTDtBQUNBLDRQQUFxTTtBQUNyTTtBQUNBLGtRQUF3TTtBQUN4TTtBQUNBLHNRQUF5TSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxodHRwLWFjY2Vzcy1mYWxsYmFja1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxwY1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERlc2t0b3BcXFxcU29seW50YV9XZWJzaXRlXFxcXGZyb250ZW5kXFxcXGxlc3Nvbi1wbGF0Zm9ybVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXGFzeW5jLW1ldGFkYXRhLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxwY1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERlc2t0b3BcXFxcU29seW50YV9XZWJzaXRlXFxcXGZyb250ZW5kXFxcXGxlc3Nvbi1wbGF0Zm9ybVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXG1ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxwY1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERlc2t0b3BcXFxcU29seW50YV9XZWJzaXRlXFxcXGZyb250ZW5kXFxcXGxlc3Nvbi1wbGF0Zm9ybVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/client-providers.tsx */ \"(rsc)/./src/app/client-providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers/ThemeProvider.tsx */ \"(rsc)/./src/app/providers/ThemeProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(rsc)/./src/components/ErrorBoundary.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/loading.tsx */ \"(rsc)/./src/app/login/loading.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3BjJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDRGVza3RvcCU1QyU1Q1NvbHludGFfV2Vic2l0ZSU1QyU1Q2Zyb250ZW5kJTVDJTVDbGVzc29uLXBsYXRmb3JtJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDbG9naW4lNUMlNUNsb2FkaW5nLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQXdKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxwY1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERlc2t0b3BcXFxcU29seW50YV9XZWJzaXRlXFxcXGZyb250ZW5kXFxcXGxlc3Nvbi1wbGF0Zm9ybVxcXFxzcmNcXFxcYXBwXFxcXGxvZ2luXFxcXGxvYWRpbmcudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3BjJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDRGVza3RvcCU1QyU1Q1NvbHludGFfV2Vic2l0ZSU1QyU1Q2Zyb250ZW5kJTVDJTVDbGVzc29uLXBsYXRmb3JtJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQXFKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxwY1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERlc2t0b3BcXFxcU29seW50YV9XZWJzaXRlXFxcXGZyb250ZW5kXFxcXGxlc3Nvbi1wbGF0Zm9ybVxcXFxzcmNcXFxcYXBwXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/client-providers.tsx":
/*!**************************************!*\
  !*** ./src/app/client-providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ClientProviders: () => (/* binding */ ClientProviders)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ClientProviders = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ClientProviders() from the server but ClientProviders is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\client-providers.tsx",
"ClientProviders",
);

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7af86ea72fdb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxPbmVEcml2ZVxcRGVza3RvcFxcRGVza3RvcFxcU29seW50YV9XZWJzaXRlXFxmcm9udGVuZFxcbGVzc29uLXBsYXRmb3JtXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3YWY4NmVhNzJmZGJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _client_providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./client-providers */ \"(rsc)/./src/app/client-providers.tsx\");\n/* harmony import */ var _providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./providers/ThemeProvider */ \"(rsc)/./src/app/providers/ThemeProvider.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(rsc)/./src/components/ErrorBoundary.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: 'Solynta Academy',\n    description: 'Digital learning platform for Nigerian students'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_client_providers__WEBPACK_IMPORTED_MODULE_3__.ClientProviders, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBMEI7QUFFSDtBQUM4QjtBQUNDO0FBQ0M7QUFFaEQsTUFBTUksV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyx3QkFBd0I7OzBCQUN0Qyw4REFBQ0M7Ozs7OzBCQWVELDhEQUFDQzswQkFDQyw0RUFBQ1YsaUVBQWFBOzhCQUNaLDRFQUFDRiw4REFBZUE7a0NBQ2QsNEVBQUNDLGdFQUFhQTtzQ0FDWE07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9mIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxPbmVEcml2ZVxcRGVza3RvcFxcRGVza3RvcFxcU29seW50YV9XZWJzaXRlXFxmcm9udGVuZFxcbGVzc29uLXBsYXRmb3JtXFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XHJcbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XHJcbmltcG9ydCB7IENsaWVudFByb3ZpZGVycyB9IGZyb20gJy4vY2xpZW50LXByb3ZpZGVycyc7XHJcbmltcG9ydCBUaGVtZVByb3ZpZGVyIGZyb20gJy4vcHJvdmlkZXJzL1RoZW1lUHJvdmlkZXInO1xyXG5pbXBvcnQgRXJyb3JCb3VuZGFyeSBmcm9tICdAL2NvbXBvbmVudHMvRXJyb3JCb3VuZGFyeSc7XHJcblxyXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xyXG4gIHRpdGxlOiAnU29seW50YSBBY2FkZW15JyxcclxuICBkZXNjcmlwdGlvbjogJ0RpZ2l0YWwgbGVhcm5pbmcgcGxhdGZvcm0gZm9yIE5pZ2VyaWFuIHN0dWRlbnRzJyxcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xyXG4gIGNoaWxkcmVuLFxyXG59OiB7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aHRtbCBsYW5nPVwiZW5cIiBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc+IFxyXG4gICAgICA8aGVhZD5cclxuICAgICAgICB7LyogTWV0YSB0YWdzIHRlbXBvcmFyaWx5IHJlbW92ZWQgKi99XHJcbiAgICAgICAgey8qIDxtZXRhIGNoYXJTZXQ9XCJ1dGYtOFwiIC8+ICovfVxyXG4gICAgICAgIHsvKiA8bWV0YSBuYW1lPVwidmlld3BvcnRcIiBjb250ZW50PVwid2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTFcIiAvPiAqL31cclxuICAgICAgICB7LyogPG1ldGFcclxuICAgICAgICAgIGh0dHBFcXVpdj1cIkNvbnRlbnQtU2VjdXJpdHktUG9saWN5XCJcclxuICAgICAgICAgIGNvbnRlbnQ9XCJkZWZhdWx0LXNyYyAnc2VsZicgaHR0cHM6Ly9hcGlzLmdvb2dsZS5jb20gaHR0cHM6Ly8qLmdvb2dsZWFwaXMuY29tIGh0dHBzOi8vKi5nc3RhdGljLmNvbTtcclxuICAgICAgICAgICAgICAgICAgIGNvbm5lY3Qtc3JjICdzZWxmJyBodHRwczovL2FwaXMuZ29vZ2xlLmNvbSBodHRwczovLyouZ29vZ2xlYXBpcy5jb20gaHR0cHM6Ly8qLmZpcmViYXNlaW8uY29tIGh0dHBzOi8vKi5jbG91ZGZ1bmN0aW9ucy5uZXQgaHR0cHM6Ly8qLmZpcmViYXNlLmdvb2dsZWFwaXMuY29tO1xyXG4gICAgICAgICAgICAgICAgICAgc2NyaXB0LXNyYyAnc2VsZicgJ3Vuc2FmZS1pbmxpbmUnICd1bnNhZmUtZXZhbCcgaHR0cHM6Ly9hcGlzLmdvb2dsZS5jb20gaHR0cHM6Ly8qLmdvb2dsZWFwaXMuY29tIGh0dHBzOi8vKi5maXJlYmFzZWlvLmNvbSBodHRwczovLyouY2xvdWRmdW5jdGlvbnMubmV0IGh0dHBzOi8vKi5nc3RhdGljLmNvbTtcclxuICAgICAgICAgICAgICAgICAgIHN0eWxlLXNyYyAnc2VsZicgJ3Vuc2FmZS1pbmxpbmUnO1xyXG4gICAgICAgICAgICAgICAgICAgaW1nLXNyYyAnc2VsZicgZGF0YTogaHR0cHM6Ly8qLmdvb2dsZWFwaXMuY29tO1xyXG4gICAgICAgICAgICAgICAgICAgZm9udC1zcmMgJ3NlbGYnIGRhdGE6O1xyXG4gICAgICAgICAgICAgICAgICAgZnJhbWUtc3JjICdzZWxmJyBodHRwczovL2FwaXMuZ29vZ2xlLmNvbSBodHRwczovLyouZ29vZ2xlLmNvbSBodHRwczovLyouZmlyZWJhc2VhcHAuY29tIGlkZW50aXR5dG9vbGtpdC5nb29nbGVhcGlzLmNvbTtcIlxyXG4gICAgICAgIC8+ICovfVxyXG4gICAgICA8L2hlYWQ+XHJcbiAgICAgIDxib2R5PlxyXG4gICAgICAgIDxFcnJvckJvdW5kYXJ5PlxyXG4gICAgICAgICAgPENsaWVudFByb3ZpZGVycz5cclxuICAgICAgICAgICAgPFRoZW1lUHJvdmlkZXI+XHJcbiAgICAgICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgICAgICA8L1RoZW1lUHJvdmlkZXI+XHJcbiAgICAgICAgICA8L0NsaWVudFByb3ZpZGVycz5cclxuICAgICAgICA8L0Vycm9yQm91bmRhcnk+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvaHRtbD5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNsaWVudFByb3ZpZGVycyIsIlRoZW1lUHJvdmlkZXIiLCJFcnJvckJvdW5kYXJ5IiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmciLCJoZWFkIiwiYm9keSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/login/loading.tsx":
/*!***********************************!*\
  !*** ./src/app/login/loading.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\login\\loading.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\login\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/providers/ThemeProvider.tsx":
/*!*********************************************!*\
  !*** ./src/app/providers/ThemeProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call the default export of \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\OneDrive\\\\\\\\Desktop\\\\\\\\Desktop\\\\\\\\Solynta_Website\\\\\\\\frontend\\\\\\\\lesson-platform\\\\\\\\src\\\\\\\\app\\\\\\\\providers\\\\\\\\ThemeProvider.tsx\\\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ThemeProvider.tsx\",\n\"default\",\n));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/providers/ThemeProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\components\\ErrorBoundary.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/client-providers.tsx */ \"(ssr)/./src/app/client-providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers/ThemeProvider.tsx */ \"(ssr)/./src/app/providers/ThemeProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(ssr)/./src/components/ErrorBoundary.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/loading.tsx */ \"(ssr)/./src/app/login/loading.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3BjJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDRGVza3RvcCU1QyU1Q1NvbHludGFfV2Vic2l0ZSU1QyU1Q2Zyb250ZW5kJTVDJTVDbGVzc29uLXBsYXRmb3JtJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDbG9naW4lNUMlNUNsb2FkaW5nLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQXdKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxwY1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERlc2t0b3BcXFxcU29seW50YV9XZWJzaXRlXFxcXGZyb250ZW5kXFxcXGxlc3Nvbi1wbGF0Zm9ybVxcXFxzcmNcXFxcYXBwXFxcXGxvZ2luXFxcXGxvYWRpbmcudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(ssr)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3BjJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDRGVza3RvcCU1QyU1Q1NvbHludGFfV2Vic2l0ZSU1QyU1Q2Zyb250ZW5kJTVDJTVDbGVzc29uLXBsYXRmb3JtJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQXFKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxwY1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERlc2t0b3BcXFxcU29seW50YV9XZWJzaXRlXFxcXGZyb250ZW5kXFxcXGxlc3Nvbi1wbGF0Zm9ybVxcXFxzcmNcXFxcYXBwXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/client-providers.tsx":
/*!**************************************!*\
  !*** ./src/app/client-providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientProviders: () => (/* binding */ ClientProviders)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(ssr)/./src/app/providers.tsx\");\n/* __next_internal_client_entry_do_not_use__ ClientProviders auto */ \n\n // Back to original providers\nfunction ClientProviders({ children }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientProviders.useEffect\": ()=>{\n            const handleRejection = {\n                \"ClientProviders.useEffect.handleRejection\": (event)=>{\n                    console.error(\"!!!! GLOBAL UNHANDLED REJECTION !!!!\", event.reason);\n                    // Handle ChunkLoadError specifically\n                    if (event.reason?.name === 'ChunkLoadError' || event.reason?.message?.includes('Loading chunk') || event.reason?.message?.includes('Loading CSS chunk')) {\n                        console.log('ChunkLoadError detected, reloading page...');\n                        event.preventDefault(); // Prevent the default unhandled rejection behavior\n                        window.location.reload();\n                        return;\n                    }\n                }\n            }[\"ClientProviders.useEffect.handleRejection\"];\n            const handleError = {\n                \"ClientProviders.useEffect.handleError\": (event)=>{\n                    console.error(\"!!!! GLOBAL ERROR !!!!\", event.error);\n                    // Handle ChunkLoadError from regular errors too\n                    if (event.error?.name === 'ChunkLoadError' || event.error?.message?.includes('Loading chunk') || event.error?.message?.includes('Loading CSS chunk')) {\n                        console.log('ChunkLoadError detected via error event, reloading page...');\n                        window.location.reload();\n                        return;\n                    }\n                }\n            }[\"ClientProviders.useEffect.handleError\"];\n            window.addEventListener('unhandledrejection', handleRejection);\n            window.addEventListener('error', handleError);\n            return ({\n                \"ClientProviders.useEffect\": ()=>{\n                    window.removeEventListener('unhandledrejection', handleRejection);\n                    window.removeEventListener('error', handleError);\n                }\n            })[\"ClientProviders.useEffect\"];\n        }\n    }[\"ClientProviders.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen\",\n            children: \"Loading application components...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\client-providers.tsx\",\n            lineNumber: 45,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\client-providers.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\client-providers.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/client-providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/login/loading.tsx":
/*!***********************************!*\
  !*** ./src/app/login/loading.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            className: \"flex flex-col items-center space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\loading.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\loading.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\loading.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\loading.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2xvZ2luL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRXVDO0FBRXhCLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDSCxpREFBTUEsQ0FBQ0UsR0FBRztZQUNURSxTQUFTO2dCQUFFQyxTQUFTO1lBQUU7WUFDdEJDLFNBQVM7Z0JBQUVELFNBQVM7WUFBRTtZQUN0QkUsTUFBTTtnQkFBRUYsU0FBUztZQUFFO1lBQ25CRixXQUFVOzs4QkFFViw4REFBQ0Q7b0JBQUlDLFdBQVU7Ozs7Ozs4QkFDZiw4REFBQ0s7b0JBQUVMLFdBQVU7OEJBQXdCOzs7Ozs7Ozs7Ozs7Ozs7OztBQUk3QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcc3JjXFxhcHBcXGxvZ2luXFxsb2FkaW5nLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmcoKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLWJhY2tncm91bmRcIj5cclxuICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAgfX1cclxuICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEgfX1cclxuICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAgfX1cclxuICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBzcGFjZS15LTRcIlxyXG4gICAgICA+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItYi0yIGJvcmRlci1wcmltYXJ5XCI+PC9kaXY+XHJcbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+TG9hZGluZy4uLjwvcD5cclxuICAgICAgPC9tb3Rpb24uZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsibW90aW9uIiwiTG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSIsImluaXRpYWwiLCJvcGFjaXR5IiwiYW5pbWF0ZSIsImV4aXQiLCJwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/loading.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _providers_AuthProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../providers/AuthProvider */ \"(ssr)/./src/app/providers/AuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n // Import useAuth\nfunction LoginPage() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [studentId, setStudentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [studentPassword, setStudentPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { handleLoginSuccess } = (0,_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_6__.useAuth)(); // Get handleLoginSuccess from context\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const handleParentLogin = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError('');\n        try {\n            // 1. Firebase Sign in\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, email, password);\n            const firebaseUser = userCredential.user;\n            if (firebaseUser) {\n                // 2. Get Firebase ID Token\n                const idToken = await firebaseUser.getIdToken();\n                // 3. Sign in to NextAuth using the token\n                const nextAuthResponse = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_5__.signIn)('credentials', {\n                    redirect: false,\n                    firebaseToken: idToken\n                });\n                if (nextAuthResponse?.error) {\n                    console.error(\"NextAuth sign-in error:\", nextAuthResponse.error);\n                    setError('Failed to establish application session. Please try again.');\n                    // Optional: Sign out from Firebase if NextAuth fails\n                    // await signOut(auth);\n                    setLoading(false);\n                    return; // Stop execution\n                } else {\n                    console.log(\"NextAuth sign-in successful for parent:\", nextAuthResponse);\n                    // 4. Redirect on successful NextAuth sign-in\n                    router.push('/dashboard');\n                }\n            }\n        } catch (err) {\n            setError('Invalid email or password');\n        } finally{\n        // setLoading(false); // setLoading(false) is handled within the success/error paths now\n        }\n    };\n    const handleStudentLogin = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError('');\n        try {\n            console.log('Attempting student login with ID:', studentId);\n            // Step 1: Call backend API to authenticate student and get custom token\n            const response = await fetch('/api/auth/student-login', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    studentId,\n                    password: studentPassword\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.success || !data.token) {\n                throw new Error(data.message || 'Student login failed or token missing');\n            }\n            console.log('Received token from backend - length:', data.token.length);\n            console.log('Token starts with:', data.token.substring(0, 50));\n            console.log('Token ends with:', data.token.substring(data.token.length - 50));\n            // Step 2: Use NextAuth credentials provider to establish session\n            const nextAuthResponse = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_5__.signIn)('credentials', {\n                redirect: false,\n                studentId,\n                studentPassword\n            });\n            if (nextAuthResponse?.error) {\n                console.error(\"NextAuth sign-in error:\", nextAuthResponse.error);\n                setError('Failed to establish application session. Please try again.');\n                setLoading(false);\n                return;\n            } // Step 3: Call AuthProvider's handleLoginSuccess with the custom token\n            // This signs in the Firebase client SDK\n            console.log(\"NextAuth sign-in successful, calling handleLoginSuccess for Firebase client...\");\n            try {\n                await handleLoginSuccess(data.token, studentId); // Pass token and studentId hint\n                console.log(\"Firebase client sign-in initiated via handleLoginSuccess. AuthProvider will redirect.\");\n            } catch (firebaseError) {\n                console.error(\"Firebase sign-in error:\", firebaseError);\n                // If Firebase sign-in fails, we still have a valid NextAuth session\n                // So we can redirect to dashboard instead of showing an error\n                console.log(\"Firebase sign-in failed, but NextAuth session is valid. Redirecting to dashboard...\");\n                router.push('/dashboard');\n                return;\n            }\n        // setLoading(false); // Keep loading until redirect happens via AuthProvider's effect\n        } catch (err) {\n            console.error(\"Student login error:\", err); // Log the actual error\n            setError(err.message || 'An error occurred during student login.');\n            setLoading(false); // Set loading false on error\n        } finally{\n        // setLoading(false); // Removed from here\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                    children: \"Sign in to your account\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 bg-red-50 border-l-4 border-red-500 p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: handleParentLogin,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-center text-2xl font-extrabold text-gray-900\",\n                                    children: \"Parent Sign in\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"email\",\n                                                name: \"email\",\n                                                type: \"email\",\n                                                autoComplete: \"email\",\n                                                required: true,\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"password\",\n                                                name: \"password\",\n                                                type: \"password\",\n                                                autoComplete: \"current-password\",\n                                                required: true,\n                                                value: password,\n                                                onChange: (e)=>setPassword(e.target.value),\n                                                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: loading,\n                                        className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Signing in...\"\n                                            ]\n                                        }, void 0, true) : 'Sign in'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full border-t border-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex justify-center text-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 bg-white text-gray-500\",\n                                            children: \"Or\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"mt-6 space-y-6\",\n                            onSubmit: handleStudentLogin,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-center text-2xl font-extrabold text-gray-900\",\n                                    children: \"Student Sign in\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"studentId\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Student ID\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"studentId\",\n                                                name: \"studentId\",\n                                                type: \"text\" // Or 'number' if appropriate\n                                                ,\n                                                autoComplete: \"username\" // Use 'username' to potentially leverage browser autofill for IDs\n                                                ,\n                                                required: true,\n                                                value: studentId,\n                                                onChange: (e)=>setStudentId(e.target.value),\n                                                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"student-password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"student-password\",\n                                                name: \"student-password\",\n                                                type: \"password\",\n                                                autoComplete: \"current-password\",\n                                                required: true,\n                                                value: studentPassword,\n                                                onChange: (e)=>setStudentPassword(e.target.value),\n                                                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: loading,\n                                        className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Signing in...\"\n                                            ]\n                                        }, void 0, true) : 'Sign in'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_NextUIProvider_nextui_org_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=NextUIProvider!=!@nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/system/dist/chunk-MNMJVVXA.mjs\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./providers/AuthProvider */ \"(ssr)/./src/app/providers/AuthProvider.tsx\");\n/* harmony import */ var _providers_SessionProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./providers/SessionProvider */ \"(ssr)/./src/app/providers/SessionProvider.tsx\");\n/* harmony import */ var _providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./providers/ClientToastWrapper */ \"(ssr)/./src/app/providers/ClientToastWrapper.tsx\");\n/* harmony import */ var _hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSessionSimple */ \"(ssr)/./src/hooks/useSessionSimple.tsx\");\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/authService */ \"(ssr)/./src/lib/authService.ts\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n// Import your AuthProvider etc.\n\n // NextAuth wrapper\n\n\n // Import the real auth function\n// Simplified SessionProvider that provides the interface expected by useSession\nfunction SimpleSessionProvider({ children }) {\n    const [backendSessionId, setBackendSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isReady, setIsReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const value = {\n        backendSessionId,\n        user,\n        setUserSession: setUser,\n        setBackendSessionId,\n        clearSession: ()=>{\n            setUser(null);\n            setBackendSessionId(null);\n        },\n        isReady,\n        isLoading,\n        getAuthHeaders: ()=>(0,_lib_authService__WEBPACK_IMPORTED_MODULE_7__.getAuthHeaders)(backendSessionId),\n        userRole: user?.role || null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_6__.SessionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleSessionProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"light\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_NextUIProvider_nextui_org_react__WEBPACK_IMPORTED_MODULE_8__.NextUIProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_5__.ClientToastWrapper, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers/AuthProvider.tsx":
/*!********************************************!*\
  !*** ./src/app/providers/AuthProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    isAuthenticated: false,\n    studentSession: null,\n    userRole: null,\n    localEffectiveRole: null,\n    isParent: false,\n    isOperator: false,\n    currentViewRole: null,\n    setCurrentViewRole: ()=>{},\n    manualSyncWithLocalStorage: ()=>{},\n    user: null,\n    userData: null,\n    childrenData: null,\n    loading: true,\n    error: null,\n    refreshUserData: async ()=>{},\n    refreshChildrenData: async ()=>{},\n    handleLoginSuccess: async ()=>{},\n    logout: async ()=>{}\n});\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [childrenData, setChildrenData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [studentSession, setStudentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize currentViewRole from localStorage\n    const [currentViewRole, setCurrentViewRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"AuthProvider.useState\": ()=>{\n            if (false) {}\n            return null;\n        }\n    }[\"AuthProvider.useState\"]);\n    // Enhanced setCurrentViewRole that persists the selection\n    const updateCurrentViewRole = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[updateCurrentViewRole]\": (role)=>{\n            console.log(`AuthProvider: Updating current view role from ${currentViewRole} to: ${role}`);\n            setCurrentViewRole(role);\n            localStorage.setItem('current_view_role', role);\n            console.log(`AuthProvider: Updated current view role to: ${role}`);\n        }\n    }[\"AuthProvider.useCallback[updateCurrentViewRole]\"], [\n        currentViewRole\n    ]);\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_6__.useSession)(); // session will be of type Session | null\n    // Synchronize AuthProvider auth state with NextAuth session\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (status === 'loading') return; // keep existing loading state until session known\n            if (status === 'authenticated' && session?.user) {\n                // Mark as authenticated\n                if (!isAuthenticated) {\n                    console.log('AuthProvider: NextAuth session detected – marking as authenticated');\n                    setIsAuthenticated(true);\n                }\n                // Store role from session if not set yet\n                const sessRole = session.user?.role;\n                if (sessRole && userRole !== sessRole) {\n                    console.log('AuthProvider: Syncing userRole from NextAuth session:', sessRole);\n                    setUserRole(sessRole);\n                }\n                if (loading) {\n                    setLoading(false);\n                }\n            } else if (status === 'unauthenticated') {\n                if (isAuthenticated) {\n                    console.log('AuthProvider: Session lost – marking as unauthenticated');\n                }\n                setIsAuthenticated(false);\n                if (loading) {\n                    setLoading(false);\n                }\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        status,\n        session,\n        isAuthenticated,\n        userRole,\n        loading\n    ]);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    // Ref to remember the last NextAuth status we saw\n    const prevAuthStatusRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Computed properties for role checking\n    const isParent = userRole === 'parent' || userRole === 'both';\n    const isOperator = userRole === 'operator' || userRole === 'both';\n    // Debug logging after all hooks are declared\n    console.log('AuthProvider: Initial state', {\n        loading,\n        isAuthenticated,\n        userRole,\n        session\n    });\n    // Calculate effective role based on actual role and current view context\n    const getEffectiveRole = ()=>{\n        // Get role from multiple sources, prioritizing session data\n        const actualRole = session?.user?.role || userData?.role || userRole || null;\n        console.log('AuthProvider: getEffectiveRole - Sources:', {\n            sessionRole: session?.user?.role,\n            userDataRole: userData?.role,\n            userRoleState: userRole,\n            actualRole,\n            currentViewRole,\n            currentPath:  false ? 0 : ''\n        });\n        if (actualRole === 'both') {\n            // For dual-role users, use the current view role or intelligently determine from path\n            const currentPath =  false ? 0 : '';\n            // If on operator path but no view role set, assume operator intent\n            if (currentPath.startsWith('/operator') && !currentViewRole) {\n                console.log('AuthProvider: On operator path with no view role, assuming operator intent');\n                return 'operator';\n            }\n            // If on parent/dashboard path but no view role set, assume parent intent  \n            if ((currentPath === '/dashboard' || currentPath.startsWith('/student-dashboard/')) && !currentViewRole) {\n                console.log('AuthProvider: On parent path with no view role, assuming parent intent');\n                return 'parent';\n            }\n            // Use the current view role or default to parent\n            return currentViewRole || 'parent';\n        }\n        return actualRole;\n    };\n    const manualSyncWithLocalStorage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[manualSyncWithLocalStorage]\": ()=>{\n            const storedUserData = localStorage.getItem('user_data');\n            if (storedUserData) {\n                setUserData(JSON.parse(storedUserData));\n            }\n            const storedChildrenData = localStorage.getItem('children_data');\n            if (storedChildrenData) {\n                setChildrenData(JSON.parse(storedChildrenData));\n            }\n        }\n    }[\"AuthProvider.useCallback[manualSyncWithLocalStorage]\"], []);\n    const refreshUserData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshUserData]\": async ()=>{\n            if (!user?.uid) {\n                console.log(\"AuthProvider: refreshUserData - No user.uid, exiting.\");\n                setUserData(null);\n                setUserRole(null);\n                setCurrentViewRole(null);\n                return;\n            }\n            console.log(`AuthProvider: Refreshing user data for UID: ${user.uid}`);\n            setError(null);\n            try {\n                // First, check custom claims for role information\n                const token = await user.getIdToken(true); // Force refresh\n                const decodedToken = await user.getIdTokenResult();\n                const customRole = decodedToken.claims.role;\n                const isParentRole = decodedToken.claims.isParent;\n                const isOperatorRole = decodedToken.claims.isOperator;\n                console.log('AuthProvider: Token claims:', {\n                    customRole,\n                    isParentRole,\n                    isOperatorRole\n                });\n                // Determine composite role\n                let finalRole = customRole;\n                if (isParentRole && isOperatorRole) {\n                    finalRole = 'both';\n                    console.log('AuthProvider: User has both parent and operator roles');\n                } else if (isParentRole) {\n                    finalRole = 'parent';\n                } else if (isOperatorRole) {\n                    finalRole = 'operator';\n                }\n                setUserRole(finalRole);\n                localStorage.setItem('user_role', finalRole);\n                // Set default view role if not set\n                if (!currentViewRole) {\n                    if (finalRole === 'both') {\n                        // Default to parent view for users with both roles\n                        setCurrentViewRole('parent');\n                        console.log('AuthProvider: Setting default view to parent for dual-role user');\n                    } else if (finalRole === 'parent' || finalRole === 'operator') {\n                        setCurrentViewRole(finalRole);\n                    }\n                }\n            // ...existing role-specific data fetching logic...\n            } catch (err) {\n                console.error('AuthProvider: Error during refreshUserData:', err);\n                setError('Failed to refresh user data. Please try logging in again.');\n                setUserData(null);\n                setUserRole(null);\n                setCurrentViewRole(null);\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshUserData]\"], [\n        user\n    ]);\n    const refreshChildrenData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshChildrenData]\": async ()=>{\n            if (!user?.uid) return;\n            try {\n                // Fetch parent dashboard which should include children data\n                const dashboardResponse = await fetch(`/api/parent/dashboard?parentId=${user.uid}`);\n                const dashboardData = await dashboardResponse.json();\n                if (dashboardResponse.ok && dashboardData.success) {\n                    const children = dashboardData.children || dashboardData.data?.children || [];\n                    setChildrenData(children);\n                    localStorage.setItem('children_data', JSON.stringify(children));\n                    // Keep a simple list of child ids in the user's doc for quick look-ups\n                    if (children.length > 0) {\n                        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, 'users', user.uid);\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.updateDoc)(parentRef, {\n                            children: children.map({\n                                \"AuthProvider.useCallback[refreshChildrenData]\": (child)=>child.id\n                            }[\"AuthProvider.useCallback[refreshChildrenData]\"])\n                        });\n                    }\n                    return; // done\n                }\n                // Fallback: dedicated children endpoint\n                const childrenResponse = await fetch(`/api/parent/get-children?parentId=${user.uid}`);\n                const childrenResData = await childrenResponse.json();\n                if (childrenResponse.ok && childrenResData.success) {\n                    const children = childrenResData.children || [];\n                    setChildrenData(children);\n                    localStorage.setItem('children_data', JSON.stringify(children));\n                    if (children.length > 0) {\n                        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, 'users', user.uid);\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.updateDoc)(parentRef, {\n                            children: children.map({\n                                \"AuthProvider.useCallback[refreshChildrenData]\": (child)=>child.id\n                            }[\"AuthProvider.useCallback[refreshChildrenData]\"])\n                        });\n                    }\n                } else {\n                    const msg = childrenResData.error || 'Failed to fetch children data';\n                    throw new Error(msg);\n                }\n            } catch (err) {\n                console.error('AuthProvider: Error fetching children data:', err);\n                setError(err instanceof Error ? err.message : 'Failed to fetch children data');\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshChildrenData]\"], [\n        user\n    ]);\n    const handleLoginSuccess = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleLoginSuccess]\": async (customToken, studentIdHint)=>{\n            // *** REMOVE ALERT ***\n            // alert(\"AuthProvider: handleLoginSuccess CALLED!\"); \n            // *** END REMOVE ALERT ***\n            console.log(\"AuthProvider: handleLoginSuccess called.\"); // Log start\n            setLoading(true);\n            setError(null);\n            try {\n                // Validate token format\n                if (!customToken || typeof customToken !== 'string') {\n                    throw new Error('Invalid token format: Token is missing or not a string.');\n                }\n                if (!customToken.includes('.')) {\n                    throw new Error('Invalid token format: Token does not appear to be a valid JWT.');\n                }\n                if (customToken.length < 50) {\n                    throw new Error('Invalid token format: Token is too short to be valid.');\n                } // Sign in using the custom token with timeout\n                console.log(\"AuthProvider: Calling signInWithCustomToken...\"); // Log before call\n                const signInPromise = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithCustomToken)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, customToken);\n                const timeoutPromise = new Promise({\n                    \"AuthProvider.useCallback[handleLoginSuccess]\": (_, reject)=>setTimeout({\n                            \"AuthProvider.useCallback[handleLoginSuccess]\": ()=>reject(new Error('Sign-in timeout: The operation took too long to complete'))\n                        }[\"AuthProvider.useCallback[handleLoginSuccess]\"], 30000)\n                }[\"AuthProvider.useCallback[handleLoginSuccess]\"]);\n                const userCredential = await Promise.race([\n                    signInPromise,\n                    timeoutPromise\n                ]);\n                const loggedInUser = userCredential.user;\n                console.log(\"AuthProvider: signInWithCustomToken successful. User:\", loggedInUser.uid);\n                // *** ADD LOGGING: Check auth.currentUser immediately after sign-in ***\n                console.log(`AuthProvider: auth.currentUser?.uid immediately after signInWithCustomToken: ${_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth.currentUser?.uid}`);\n                // Force token refresh to get custom claims\n                console.log(\"AuthProvider: Forcing token refresh (getIdToken(true))...\"); // Log before refresh\n                await loggedInUser.getIdToken(true);\n                console.log(\"AuthProvider: Token refresh complete.\"); // Log after refresh\n                // *** ADD LOGGING: Check auth.currentUser after token refresh ***\n                console.log(`AuthProvider: auth.currentUser?.uid after token refresh: ${_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth.currentUser?.uid}`);\n                // Use provided studentId or look in localStorage - BUT DO NOT SET AS SESSION ID\n                const studentId = studentIdHint || localStorage.getItem('student_id');\n                if (studentId) {\n                    // Only store the student ID, not as a session ID\n                    localStorage.setItem('student_id', studentId);\n                    // Remove any existing incorrect session ID that might be the student ID\n                    if (localStorage.getItem('current_session') === studentId) {\n                        console.log(\"AuthProvider: Removing incorrect session ID (was set to student ID)\");\n                        localStorage.removeItem('current_session');\n                    }\n                    // Set claims via API immediately after login\n                    try {\n                        const idToken = await loggedInUser.getIdToken();\n                        const claimResponse = await fetch('/api/auth/set-student-claims', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json',\n                                'Authorization': `Bearer ${idToken}`\n                            },\n                            body: JSON.stringify({\n                                studentId: studentId\n                            })\n                        });\n                        if (claimResponse.ok) {\n                            console.log(\"AuthProvider: Student claims set successfully\");\n                            // Force another token refresh to get the new claims\n                            await loggedInUser.getIdToken(true);\n                        } else {\n                            let errorMessage = `Status: ${claimResponse.status}`;\n                            try {\n                                const errorData = await claimResponse.json();\n                                errorMessage = errorData.error || errorMessage;\n                            } catch (jsonError) {\n                                // If response is not JSON, try to get text\n                                try {\n                                    const errorText = await claimResponse.text();\n                                    errorMessage = errorText || errorMessage;\n                                } catch (textError) {\n                                    console.error(\"AuthProvider: Could not parse error response:\", textError);\n                                }\n                            }\n                            console.error(\"AuthProvider: Failed to set student claims:\", errorMessage);\n                        }\n                    } catch (e) {\n                        console.error(\"AuthProvider: Error setting student claims:\", e);\n                    }\n                }\n                // Clear any parent-related flags first\n                localStorage.removeItem('parent_id');\n                localStorage.removeItem('parent_name');\n                localStorage.removeItem('parent_role');\n                localStorage.removeItem('viewing_as_child');\n                localStorage.removeItem('is_parent');\n                // Set user role and auth state\n                localStorage.setItem('user_role', 'student');\n                setUserRole('student');\n                setIsAuthenticated(true);\n                // Clear the progress flag after successful sign-in\n                localStorage.removeItem('login_in_progress');\n                console.log(\"AuthProvider: Login successful, cleared login_in_progress flag.\");\n            } catch (err) {\n                console.error(\"AuthProvider: Error signing in with custom token:\", err);\n                let errorMessage = \"Failed to sign in with custom token.\";\n                if (err instanceof Error) {\n                    if (err.message.includes('auth/quota-exceeded')) {\n                        errorMessage = \"Authentication quota exceeded. Please try again later.\";\n                    } else if (err.message.includes('auth/invalid-custom-token')) {\n                        errorMessage = \"Invalid authentication token. Please try logging in again.\";\n                    } else if (err.message.includes('auth/custom-token-mismatch')) {\n                        errorMessage = \"Authentication token mismatch. Please try logging in again.\";\n                    } else if (err.message.includes('auth/network-request-failed')) {\n                        errorMessage = \"Network error. Please check your internet connection and try again.\";\n                    } else {\n                        errorMessage = err.message;\n                    }\n                }\n                setError(errorMessage);\n                setUser(null);\n                setUserData(null);\n                setChildrenData(null);\n                setUserRole(null);\n                setStudentSession(null);\n                // Clear flags/storage on error\n                localStorage.removeItem('auth_token');\n                localStorage.removeItem('student_id');\n                localStorage.removeItem('user_role');\n                localStorage.removeItem('current_session');\n                localStorage.removeItem('login_in_progress');\n                // Re-throw the error so it can be caught by the caller\n                throw err;\n            } finally{\n                // setLoading(false); // Loading should be set to false by the onAuthStateChanged listener handling\n                console.log(\"AuthProvider: handleLoginSuccess finally block.\"); // Log finally\n            }\n        }\n    }[\"AuthProvider.useCallback[handleLoginSuccess]\"], []); // Removed auth dependency as it's globally available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            console.log(\"AuthProvider: Setting up onAuthStateChanged listener.\"); // Log listener setup\n            const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, {\n                \"AuthProvider.useEffect.unsubscribe\": async (user)=>{\n                    // *** ADD DETAILED LOGGING for onAuthStateChanged ***\n                    const timestamp = new Date().toISOString();\n                    console.log(`AuthProvider: onAuthStateChanged fired at ${timestamp}. User object:`, user ? {\n                        uid: user.uid,\n                        email: user.email\n                    } : null);\n                    setUser(user); // Update the user state\n                    if (user) {\n                        console.log(`AuthProvider: onAuthStateChanged - User is present (UID: ${user.uid}).`);\n                        // Try to refresh token to get latest claims\n                        try {\n                            console.log(\"AuthProvider: onAuthStateChanged - Refreshing token...\");\n                            await user.getIdToken(true);\n                            console.log(\"AuthProvider: onAuthStateChanged - Token refreshed.\");\n                            // ... check claims ...\n                            const idTokenResult = await user.getIdTokenResult();\n                            console.log(\"AuthProvider: onAuthStateChanged - Token claims:\", idTokenResult.claims);\n                        // ... set role/session from claims ...\n                        } catch (e) {\n                            console.error(\"AuthProvider: onAuthStateChanged - Error refreshing token:\", e);\n                        }\n                        // Try to load from localStorage first for faster initial render\n                        const storedUserData = localStorage.getItem('user_data');\n                        if (storedUserData) {\n                            setUserData(JSON.parse(storedUserData));\n                        }\n                        const storedChildrenData = localStorage.getItem('children_data');\n                        if (storedChildrenData) {\n                            setChildrenData(JSON.parse(storedChildrenData));\n                        }\n                        // Then refresh from server\n                        console.log(\"AuthProvider: onAuthStateChanged - Calling refreshUserData...\");\n                        await refreshUserData();\n                        console.log(\"AuthProvider: onAuthStateChanged - refreshUserData complete.\");\n                        setIsAuthenticated(true);\n                        console.log(\"AuthProvider: onAuthStateChanged - Set isAuthenticated = true.\");\n                        // Only refresh children data if user is a parent\n                        if (userData?.role === 'parent' || JSON.parse(storedUserData || '{}')?.role === 'parent') {\n                            await refreshChildrenData();\n                        }\n                    } else {\n                        console.log(\"AuthProvider: onAuthStateChanged - User is null.\");\n                        // ... clear user state and localStorage ...\n                        setUserData(null);\n                        setChildrenData(null);\n                        setUserRole(null);\n                        setStudentSession(null);\n                        setIsAuthenticated(false);\n                        localStorage.removeItem('user_data');\n                        localStorage.removeItem('children_data');\n                        localStorage.removeItem('CURRENT_SESSION_KEY'); // Ensure correct key if used elsewhere\n                        localStorage.removeItem('user_role');\n                        localStorage.removeItem('student_id');\n                        console.log(\"AuthProvider: onAuthStateChanged - Cleared state and localStorage.\");\n                    }\n                    console.log(`AuthProvider: onAuthStateChanged - Setting loading = false at ${new Date().toISOString()}.`);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.unsubscribe\"]);\n            // Cleanup function\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    console.log(\"AuthProvider: Cleaning up onAuthStateChanged listener.\"); // Log cleanup\n                    unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        refreshUserData,\n        refreshChildrenData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Skip if still loading auth or session\n            if (loading || status === 'loading') return;\n            // Handle user authentication state for both Firebase and NextAuth\n            const sessionUser = session?.user;\n            const isAuthenticated = !!user && !!user.uid || status === 'authenticated';\n            if (isAuthenticated) {\n                // If user is authenticated but no role is set, attempt to determine role\n                if (!userRole) {\n                    console.log(\"AuthProvider: User authenticated but no role set. Attempting to determine role...\");\n                    // First check NextAuth session for role (for parents/operators)\n                    if (status === 'authenticated' && sessionUser?.role) {\n                        console.log(`AuthProvider: Found role in NextAuth session: ${sessionUser.role}`);\n                        // For NextAuth users, get comprehensive role information from API\n                        const fetchUserRole = {\n                            \"AuthProvider.useEffect.fetchUserRole\": async ()=>{\n                                try {\n                                    console.log('AuthProvider: Attempting to fetch user role from API...');\n                                    const response = await fetch('/api/auth/get-user-role', {\n                                        method: 'GET',\n                                        credentials: 'include',\n                                        headers: {\n                                            'Content-Type': 'application/json'\n                                        }\n                                    });\n                                    console.log('AuthProvider: API response status:', response.status);\n                                    if (response.ok) {\n                                        const roleData = await response.json();\n                                        if (roleData.success) {\n                                            console.log(`AuthProvider: API returned role: ${roleData.role}`, roleData);\n                                            setUserRole(roleData.role);\n                                            localStorage.setItem('user_role', roleData.role);\n                                            // Set current view role based on the comprehensive role\n                                            if (roleData.role === 'both') {\n                                                // For dual-role users, check if there's a preferred view stored or default to parent\n                                                const storedViewRole = localStorage.getItem('current_view_role');\n                                                const preferredView = storedViewRole || 'parent';\n                                                setCurrentViewRole(preferredView);\n                                                localStorage.setItem('current_view_role', preferredView);\n                                                console.log(`AuthProvider: Set view role to ${preferredView} for dual-role user`);\n                                            } else if (roleData.role === 'parent' || roleData.role === 'operator') {\n                                                setCurrentViewRole(roleData.role);\n                                                localStorage.setItem('current_view_role', roleData.role);\n                                            }\n                                            return;\n                                        } else {\n                                            console.log('AuthProvider: API returned unsuccessful response:', roleData);\n                                        }\n                                    } else {\n                                        console.log('AuthProvider: API returned error status:', response.status);\n                                    }\n                                    // Fallback to session role if API fails\n                                    console.log('AuthProvider: API failed, using session role as fallback');\n                                    if (sessionUser?.role) {\n                                        setUserRole(sessionUser.role);\n                                        localStorage.setItem('user_role', sessionUser.role);\n                                        // Set view role for session fallback\n                                        if (sessionUser.role === 'both') {\n                                            const storedViewRole = localStorage.getItem('current_view_role');\n                                            const preferredView = storedViewRole || 'parent';\n                                            setCurrentViewRole(preferredView);\n                                            localStorage.setItem('current_view_role', preferredView);\n                                        } else if (sessionUser.role === 'parent' || sessionUser.role === 'operator') {\n                                            setCurrentViewRole(sessionUser.role);\n                                            localStorage.setItem('current_view_role', sessionUser.role);\n                                        }\n                                    }\n                                } catch (error) {\n                                    console.error('AuthProvider: Error fetching user role:', error);\n                                    // Fallback to session role on network error\n                                    if (sessionUser?.role) {\n                                        console.log('AuthProvider: Using session role as fallback due to error');\n                                        setUserRole(sessionUser.role);\n                                        localStorage.setItem('user_role', sessionUser.role);\n                                        // Set view role for error fallback\n                                        if (sessionUser.role === 'both') {\n                                            const storedViewRole = localStorage.getItem('current_view_role');\n                                            const preferredView = storedViewRole || 'parent';\n                                            setCurrentViewRole(preferredView);\n                                            localStorage.setItem('current_view_role', preferredView);\n                                        } else if (sessionUser.role === 'parent' || sessionUser.role === 'operator') {\n                                            setCurrentViewRole(sessionUser.role);\n                                            localStorage.setItem('current_view_role', sessionUser.role);\n                                        }\n                                    }\n                                }\n                            }\n                        }[\"AuthProvider.useEffect.fetchUserRole\"];\n                        fetchUserRole();\n                        return;\n                    }\n                    // Check for role in localStorage\n                    const storedRole = localStorage.getItem('user_role');\n                    if (storedRole) {\n                        console.log(`AuthProvider: Found role in localStorage: ${storedRole}`);\n                        setUserRole(storedRole);\n                        // If this is a student, check for student ID\n                        if (storedRole === 'student') {\n                            const storedStudentId = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                            if (storedStudentId) {\n                                console.log(`AuthProvider: Found student ID in localStorage: ${storedStudentId}`);\n                                setStudentSession(storedStudentId);\n                            }\n                        }\n                    } else if (user) {\n                        // Firebase user specific logic\n                        // Check for parent indicators\n                        const isParent = localStorage.getItem('parent_id') || localStorage.getItem('parent_name');\n                        // Check for temporary student session (parent viewing student dashboard)\n                        const tempStudentSession = localStorage.getItem('temp_student_session');\n                        if (isParent) {\n                            console.log(\"AuthProvider: User appears to be a parent based on localStorage\");\n                            setUserRole('parent');\n                            // If parent is viewing a student dashboard, set the student session\n                            if (tempStudentSession) {\n                                console.log(`AuthProvider: Parent viewing student dashboard for: ${tempStudentSession}`);\n                                setStudentSession(tempStudentSession);\n                            }\n                        } else {\n                            // Default to student role if no other indicators\n                            console.log(\"AuthProvider: No role indicators found, defaulting to student\");\n                            setUserRole('student');\n                            // Try to find student ID\n                            const storedStudentId = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                            if (storedStudentId) {\n                                console.log(`AuthProvider: Found student ID: ${storedStudentId}`);\n                                setStudentSession(storedStudentId);\n                            } else {\n                                console.log(`AuthProvider: No student ID found, using UID: ${user.uid}`);\n                                setStudentSession(user.uid);\n                                localStorage.setItem('student_id', user.uid);\n                                localStorage.setItem('current_session', user.uid);\n                            }\n                        }\n                    }\n                }\n            // End of role determination logic\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        user,\n        userRole,\n        setUserRole,\n        setStudentSession,\n        status,\n        session\n    ]);\n    // ---------------------------\n    // SECOND useEffect – Navigation / Redirection logic\n    // ---------------------------\n    // ---------------------------\n    // REVISED Navigation / Redirection effect\n    // This replaces the old, complex useEffect with a simpler, more declarative approach\n    // ---------------------------\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Wait until auth state & Next-Auth session have finished loading\n            if (loading || status === 'loading') {\n                console.log('AuthProvider Nav: Waiting for auth to complete...', {\n                    loading,\n                    status\n                });\n                return;\n            }\n            const currentPath =  false ? 0 : '';\n            const sessionRole = session?.user?.role;\n            const isNextAuthAuthenticated = status === 'authenticated' && !!sessionRole;\n            const hasSessionWithRole = !!session && !!session?.user?.role;\n            console.log('AuthProvider Nav: DEBUG', {\n                currentPath,\n                loading,\n                status,\n                isNextAuthAuthenticated,\n                hasSessionWithRole,\n                sessionRole,\n                currentViewRole,\n                session: !!session,\n                sessionUser: session?.user,\n                firebaseUser: !!user,\n                sessionUserRole: session?.user?.role,\n                rawSession: session\n            });\n            // --- 1. Handle Unauthenticated Users ---\n            // Updated authentication check to be more robust\n            const isReallyAuthenticated = isNextAuthAuthenticated || hasSessionWithRole;\n            console.log('AuthProvider Nav: Authentication status:', {\n                isNextAuthAuthenticated,\n                hasSessionWithRole,\n                isReallyAuthenticated,\n                statusCheck: status === 'authenticated',\n                sessionRoleCheck: !!sessionRole,\n                hasSessionCheck: !!session,\n                sessionUserRoleCheck: !!session?.user?.role\n            });\n            // SPECIAL CASE: If we're on any dashboard path and have any session, don't redirect\n            // EXCEPT for dual-role users who need navigation between dashboards\n            if ((currentPath.startsWith('/operator') || currentPath === '/dashboard' || currentPath.startsWith('/dashboard/')) && session && session?.user?.role) {\n                // Allow dual-role navigation to proceed\n                if (sessionRole === 'both') {\n                    console.log('AuthProvider Nav: On dashboard path with dual-role session, allowing dual-role navigation logic');\n                // Don't return here - let dual-role navigation logic run\n                } else {\n                    console.log('AuthProvider Nav: On dashboard path with valid session, allowing access');\n                    return;\n                }\n            }\n            if (!isReallyAuthenticated) {\n                const publicPaths = [\n                    '/login',\n                    '/register',\n                    '/forgot-password',\n                    '/reset'\n                ];\n                // If not on a public path, redirect to login.\n                if (!publicPaths.some({\n                    \"AuthProvider.useEffect\": (p)=>currentPath.startsWith(p)\n                }[\"AuthProvider.useEffect\"])) {\n                    console.log(`AuthProvider Nav: Not authenticated, redirecting from ${currentPath} to /login`);\n                    console.log('AuthProvider Nav: Redirect reason - authentication check failed', {\n                        currentPath,\n                        isNextAuthAuthenticated,\n                        hasSessionWithRole,\n                        session: session,\n                        sessionUser: session?.user,\n                        status,\n                        sessionRole\n                    });\n                    router.replace('/login');\n                } else {\n                    console.log('AuthProvider Nav: On public path, no redirect needed');\n                }\n                return;\n            }\n            console.log('AuthProvider Nav: User is authenticated, proceeding with navigation logic');\n            // --- 2. Handle Authenticated Users ---\n            // If user is on the login page, get them out of there.\n            if (currentPath.startsWith('/login')) {\n                // For dual-role users, go to the dashboard matching their selected view.\n                const destination = sessionRole === 'both' ? currentViewRole === 'operator' ? '/operator/dashboard' : '/dashboard' : '/dashboard'; // Default for parents\n                console.log(`AuthProvider Nav: Authenticated on login page, redirecting to ${destination}`);\n                router.replace(destination);\n                return;\n            }\n            // --- 3. Handle Dual-Role Navigation ---\n            // This is the declarative navigation that replaces the setTimeout logic.\n            if (sessionRole === 'both') {\n                // If the user's intent (currentViewRole) doesn't match their location, navigate them.\n                if (currentViewRole === 'operator' && !currentPath.startsWith('/operator')) {\n                    console.log(\"AuthProvider Nav: Dual-role user wants 'operator' view, navigating to operator dashboard.\");\n                    router.replace('/operator/dashboard');\n                    return; // Stop further processing after navigation\n                }\n                if (currentViewRole === 'parent' && currentPath.startsWith('/operator')) {\n                    console.log(\"AuthProvider Nav: Dual-role user wants 'parent' view, navigating to parent dashboard.\");\n                    router.replace('/dashboard');\n                    return; // Stop further processing after navigation\n                }\n                // If a dual-role user is on either of their valid dashboards, DO NOTHING. Let them be.\n                console.log(\"AuthProvider Nav: Dual-role user on a valid path, no redirect needed.\");\n                return;\n            }\n            // --- 4. Handle Single-Role Users on Wrong Page ---\n            // This logic now only applies to users who are NOT dual-role.\n            if (sessionRole === 'parent' && currentPath.startsWith('/operator')) {\n                console.log(`AuthProvider Nav: Parent on operator page, redirecting to /dashboard.`);\n                router.replace('/dashboard');\n            }\n        // Note: A single-role 'operator' would be handled by the logic in step 2 if they land on '/'.\n        // This covers all critical cases.\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        status,\n        session,\n        currentViewRole,\n        router\n    ]); // Dependencies that drive navigation\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            userData,\n            childrenData,\n            loading,\n            error,\n            isAuthenticated: !!user && !!user.uid || status === 'authenticated' || !!session && !!session?.user?.role,\n            // Use SessionUser type for fallbacks\n            studentSession: studentSession || (session?.user?.role === 'student' ? session?.user?.id : null),\n            userRole: session?.user?.role || userData?.role || userRole || null,\n            localEffectiveRole: getEffectiveRole(),\n            isParent: (session?.user?.role || userData?.role || userRole) === 'parent' || (session?.user?.role || userData?.role || userRole) === 'both',\n            isOperator: (session?.user?.role || userData?.role || userRole) === 'operator' || (session?.user?.role || userData?.role || userRole) === 'both',\n            currentViewRole,\n            setCurrentViewRole: updateCurrentViewRole,\n            manualSyncWithLocalStorage,\n            refreshUserData,\n            refreshChildrenData,\n            handleLoginSuccess,\n            logout: async ()=>{\n                console.log(\"AuthProvider: Logging out...\");\n                try {\n                    await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth);\n                    // Clear backend performance caches\n                    try {\n                        const cacheResponse = await fetch('/api/clear-performance-cache', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        if (cacheResponse.ok) {\n                            console.log(\"AuthProvider: Backend caches cleared successfully\");\n                        } else {\n                            console.warn(\"AuthProvider: Failed to clear backend caches, but continuing with logout\");\n                        }\n                    } catch (cacheError) {\n                        console.warn(\"AuthProvider: Error clearing backend caches:\", cacheError);\n                    // Don't fail logout due to cache clearing issues\n                    }\n                    // Clear local state and storage\n                    setUser(null);\n                    setUserData(null);\n                    setChildrenData(null);\n                    setUserRole(null);\n                    setStudentSession(null);\n                    setCurrentViewRole(null);\n                    setIsAuthenticated(false);\n                    // Clear all browser storage\n                    localStorage.clear();\n                    sessionStorage.clear();\n                    // Clear IndexedDB (if any data is stored there)\n                    try {\n                        if ('indexedDB' in window) {\n                            const databases = await indexedDB.databases();\n                            databases.forEach(async (db)=>{\n                                if (db.name) {\n                                    indexedDB.deleteDatabase(db.name);\n                                }\n                            });\n                        }\n                    } catch (idbError) {\n                        console.warn(\"AuthProvider: Error clearing IndexedDB:\", idbError);\n                    }\n                    // Clear service worker caches\n                    try {\n                        if ('caches' in window) {\n                            const cacheNames = await caches.keys();\n                            await Promise.all(cacheNames.map((cacheName)=>caches.delete(cacheName)));\n                            console.log(\"AuthProvider: Service worker caches cleared\");\n                        }\n                    } catch (swError) {\n                        console.warn(\"AuthProvider: Error clearing service worker caches:\", swError);\n                    }\n                    console.log(\"AuthProvider: Logout successful. All caches cleared. Redirecting to login.\");\n                    router.push('/login');\n                } catch (err) {\n                    console.error(\"AuthProvider: Logout failed:\", err);\n                    setError(\"Logout failed. Please try again.\");\n                    // Still attempt to clear state/storage even if signOut fails\n                    setUser(null);\n                    setUserData(null);\n                    setChildrenData(null);\n                    setUserRole(null);\n                    setStudentSession(null);\n                    setCurrentViewRole(null);\n                    setIsAuthenticated(false);\n                    localStorage.clear();\n                    sessionStorage.clear();\n                    router.push('/login'); // Redirect even on error\n                }\n            }\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\AuthProvider.tsx\",\n        lineNumber: 797,\n        columnNumber: 3\n    }, this);\n}\nconst useAuth = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy9BdXRoUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUc7QUFDVjtBQUMvQztBQUNFO0FBQ2dCO0FBQ2Y7QUFzQzdDLE1BQU1nQiw0QkFBY2Ysb0RBQWFBLENBQWtCO0lBQ2pEZ0IsaUJBQWlCO0lBQ2pCQyxnQkFBZ0I7SUFDaEJDLFVBQVU7SUFDVkMsb0JBQW9CO0lBQ3BCQyxVQUFVO0lBQ1ZDLFlBQVk7SUFDWkMsaUJBQWlCO0lBQ2pCQyxvQkFBb0IsS0FBTztJQUMzQkMsNEJBQTRCLEtBQU87SUFDbkNDLE1BQU07SUFDTkMsVUFBVTtJQUNWQyxjQUFjO0lBQ2RDLFNBQVM7SUFDVEMsT0FBTztJQUNQQyxpQkFBaUIsV0FBYTtJQUM5QkMscUJBQXFCLFdBQWE7SUFDbENDLG9CQUFvQixXQUFhO0lBQ2pDQyxRQUFRLFdBQWE7QUFDdkI7QUFFTyxTQUFTQyxhQUFhLEVBQUVDLFFBQVEsRUFBaUM7SUFDdEUsTUFBTSxDQUFDVixNQUFNVyxRQUFRLEdBQUdqQywrQ0FBUUEsQ0FBYztJQUM5QyxNQUFNLENBQUN1QixVQUFVVyxZQUFZLEdBQUdsQywrQ0FBUUEsQ0FBYTtJQUNyRCxNQUFNLENBQUN3QixjQUFjVyxnQkFBZ0IsR0FBR25DLCtDQUFRQSxDQUFlO0lBQy9ELE1BQU0sQ0FBQ3lCLFNBQVNXLFdBQVcsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQzBCLE9BQU9XLFNBQVMsR0FBR3JDLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNhLGlCQUFpQnlCLG1CQUFtQixHQUFHdEMsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDYyxnQkFBZ0J5QixrQkFBa0IsR0FBR3ZDLCtDQUFRQSxDQUFhO0lBQ2pFLE1BQU0sQ0FBQ2UsVUFBVXlCLFlBQVksR0FBR3hDLCtDQUFRQSxDQUFnQjtJQUV4RCwrQ0FBK0M7SUFDL0MsTUFBTSxDQUFDbUIsaUJBQWlCQyxtQkFBbUIsR0FBR3BCLCtDQUFRQTtpQ0FBK0I7WUFDbkYsSUFBSSxLQUE2QixFQUFFLEVBRWxDO1lBQ0QsT0FBTztRQUNUOztJQUVBLDBEQUEwRDtJQUMxRCxNQUFNMkMsd0JBQXdCMUMsa0RBQVdBOzJEQUFDLENBQUMyQztZQUN6Q0MsUUFBUUMsR0FBRyxDQUFDLENBQUMsOENBQThDLEVBQUUzQixnQkFBZ0IsS0FBSyxFQUFFeUIsTUFBTTtZQUMxRnhCLG1CQUFtQndCO1lBQ25CSCxhQUFhTSxPQUFPLENBQUMscUJBQXFCSDtZQUMxQ0MsUUFBUUMsR0FBRyxDQUFDLENBQUMsNENBQTRDLEVBQUVGLE1BQU07UUFDbkU7MERBQUc7UUFBQ3pCO0tBQWdCO0lBQ3BCLE1BQU0sRUFBRTZCLE1BQU1DLE9BQU8sRUFBRUMsTUFBTSxFQUFFLEdBQUd2QywyREFBVUEsSUFBSSx5Q0FBeUM7SUFFekYsNERBQTREO0lBQzVEWixnREFBU0E7a0NBQUM7WUFDUixJQUFJbUQsV0FBVyxXQUFXLFFBQVEsa0RBQWtEO1lBRXBGLElBQUlBLFdBQVcsbUJBQW1CRCxTQUFTM0IsTUFBTTtnQkFDL0Msd0JBQXdCO2dCQUN4QixJQUFJLENBQUNULGlCQUFpQjtvQkFDcEJnQyxRQUFRQyxHQUFHLENBQUM7b0JBQ1pSLG1CQUFtQjtnQkFDckI7Z0JBQ0EseUNBQXlDO2dCQUN6QyxNQUFNYSxXQUFZRixRQUFRM0IsSUFBSSxFQUFrQnNCO2dCQUNoRCxJQUFJTyxZQUFZcEMsYUFBYW9DLFVBQVU7b0JBQ3JDTixRQUFRQyxHQUFHLENBQUMseURBQXlESztvQkFDckVYLFlBQVlXO2dCQUNkO2dCQUNBLElBQUkxQixTQUFTO29CQUNYVyxXQUFXO2dCQUNiO1lBQ0YsT0FBTyxJQUFJYyxXQUFXLG1CQUFtQjtnQkFDdkMsSUFBSXJDLGlCQUFpQjtvQkFDbkJnQyxRQUFRQyxHQUFHLENBQUM7Z0JBQ2Q7Z0JBQ0FSLG1CQUFtQjtnQkFDbkIsSUFBSWIsU0FBUztvQkFDWFcsV0FBVztnQkFDYjtZQUNGO1FBQ0Y7aUNBQUc7UUFBQ2M7UUFBUUQ7UUFBU3BDO1FBQWlCRTtRQUFVVTtLQUFRO0lBQ3hELE1BQU0yQixTQUFTNUMsMERBQVNBO0lBRXhCLGtEQUFrRDtJQUNsRCxNQUFNNkMsb0JBQW9CbkQsNkNBQU1BLENBQWdCO0lBRWhELHdDQUF3QztJQUN4QyxNQUFNZSxXQUFXRixhQUFhLFlBQVlBLGFBQWE7SUFDdkQsTUFBTUcsYUFBYUgsYUFBYSxjQUFjQSxhQUFhO0lBRTNELDZDQUE2QztJQUM3QzhCLFFBQVFDLEdBQUcsQ0FBQywrQkFBK0I7UUFBRXJCO1FBQVNaO1FBQWlCRTtRQUFVa0M7SUFBUTtJQUV6Rix5RUFBeUU7SUFDekUsTUFBTUssbUJBQW1CO1FBQ3ZCLDREQUE0RDtRQUM1RCxNQUFNQyxhQUFhLFNBQVVqQyxNQUFzQnNCLFFBQVFyQixVQUFVcUIsUUFBUTdCLFlBQVk7UUFFekY4QixRQUFRQyxHQUFHLENBQUMsNkNBQTZDO1lBQ3ZEVSxhQUFjUCxTQUFTM0IsTUFBc0JzQjtZQUM3Q2EsY0FBY2xDLFVBQVVxQjtZQUN4QmMsZUFBZTNDO1lBQ2Z3QztZQUNBcEM7WUFDQXdDLGFBQWEsTUFBNkIsR0FBR0MsQ0FBd0IsR0FBRztRQUMxRTtRQUVBLElBQUlMLGVBQWUsUUFBUTtZQUN6QixzRkFBc0Y7WUFDdEYsTUFBTUksY0FBYyxNQUE2QixHQUFHQyxDQUF3QixHQUFHO1lBRS9FLG1FQUFtRTtZQUNuRSxJQUFJRCxZQUFZSSxVQUFVLENBQUMsZ0JBQWdCLENBQUM1QyxpQkFBaUI7Z0JBQzNEMEIsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLE9BQU87WUFDVDtZQUVBLDJFQUEyRTtZQUMzRSxJQUFJLENBQUNhLGdCQUFnQixnQkFBZ0JBLFlBQVlJLFVBQVUsQ0FBQyxzQkFBcUIsS0FBTSxDQUFDNUMsaUJBQWlCO2dCQUN2RzBCLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixPQUFPO1lBQ1Q7WUFFQSxpREFBaUQ7WUFDakQsT0FBTzNCLG1CQUFtQjtRQUM1QjtRQUVBLE9BQU9vQztJQUNUO0lBRUEsTUFBTWxDLDZCQUE2QnBCLGtEQUFXQTtnRUFBQztZQUM3QyxNQUFNK0QsaUJBQWlCdkIsYUFBYUMsT0FBTyxDQUFDO1lBQzVDLElBQUlzQixnQkFBZ0I7Z0JBQ2xCOUIsWUFBWStCLEtBQUtDLEtBQUssQ0FBQ0Y7WUFDekI7WUFDQSxNQUFNRyxxQkFBcUIxQixhQUFhQyxPQUFPLENBQUM7WUFDaEQsSUFBSXlCLG9CQUFvQjtnQkFDdEJoQyxnQkFBZ0I4QixLQUFLQyxLQUFLLENBQUNDO1lBQzdCO1FBQ0Y7K0RBQUcsRUFBRTtJQUVMLE1BQU14QyxrQkFBa0IxQixrREFBV0E7cURBQUM7WUFDbEMsSUFBSSxDQUFDcUIsTUFBTThDLEtBQUs7Z0JBQ2R2QixRQUFRQyxHQUFHLENBQUM7Z0JBQ1paLFlBQVk7Z0JBQ1pNLFlBQVk7Z0JBQ1pwQixtQkFBbUI7Z0JBQ25CO1lBQ0Y7WUFDQXlCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDRDQUE0QyxFQUFFeEIsS0FBSzhDLEdBQUcsRUFBRTtZQUNyRS9CLFNBQVM7WUFFVCxJQUFJO2dCQUNGLGtEQUFrRDtnQkFDbEQsTUFBTWdDLFFBQVEsTUFBTS9DLEtBQUtnRCxVQUFVLENBQUMsT0FBTyxnQkFBZ0I7Z0JBQzNELE1BQU1DLGVBQWUsTUFBTWpELEtBQUtrRCxnQkFBZ0I7Z0JBRWhELE1BQU1DLGFBQWFGLGFBQWFHLE1BQU0sQ0FBQzlCLElBQUk7Z0JBQzNDLE1BQU0rQixlQUFlSixhQUFhRyxNQUFNLENBQUN6RCxRQUFRO2dCQUNqRCxNQUFNMkQsaUJBQWlCTCxhQUFhRyxNQUFNLENBQUN4RCxVQUFVO2dCQUVyRDJCLFFBQVFDLEdBQUcsQ0FBQywrQkFBK0I7b0JBQUUyQjtvQkFBWUU7b0JBQWNDO2dCQUFlO2dCQUV0RiwyQkFBMkI7Z0JBQzNCLElBQUlDLFlBQVlKO2dCQUNoQixJQUFJRSxnQkFBZ0JDLGdCQUFnQjtvQkFDbENDLFlBQVk7b0JBQ1poQyxRQUFRQyxHQUFHLENBQUM7Z0JBQ2QsT0FBTyxJQUFJNkIsY0FBYztvQkFDdkJFLFlBQVk7Z0JBQ2QsT0FBTyxJQUFJRCxnQkFBZ0I7b0JBQ3pCQyxZQUFZO2dCQUNkO2dCQUVBckMsWUFBWXFDO2dCQUNacEMsYUFBYU0sT0FBTyxDQUFDLGFBQWE4QjtnQkFFbEMsbUNBQW1DO2dCQUNuQyxJQUFJLENBQUMxRCxpQkFBaUI7b0JBQ3BCLElBQUkwRCxjQUFjLFFBQVE7d0JBQ3hCLG1EQUFtRDt3QkFDbkR6RCxtQkFBbUI7d0JBQ25CeUIsUUFBUUMsR0FBRyxDQUFDO29CQUNkLE9BQU8sSUFBSStCLGNBQWMsWUFBWUEsY0FBYyxZQUFZO3dCQUM3RHpELG1CQUFtQnlEO29CQUNyQjtnQkFDRjtZQUVBLG1EQUFtRDtZQUNyRCxFQUFFLE9BQU9DLEtBQUs7Z0JBQ1pqQyxRQUFRbkIsS0FBSyxDQUFDLCtDQUErQ29EO2dCQUM3RHpDLFNBQVM7Z0JBQ1RILFlBQVk7Z0JBQ1pNLFlBQVk7Z0JBQ1pwQixtQkFBbUI7WUFDckI7UUFDRjtvREFBRztRQUFDRTtLQUFLO0lBRVQsTUFBTU0sc0JBQXNCM0Isa0RBQVdBO3lEQUFDO1lBQ3RDLElBQUksQ0FBQ3FCLE1BQU04QyxLQUFLO1lBRWhCLElBQUk7Z0JBQ0YsNERBQTREO2dCQUM1RCxNQUFNVyxvQkFBb0IsTUFBTUMsTUFBTSxDQUFDLCtCQUErQixFQUFFMUQsS0FBSzhDLEdBQUcsRUFBRTtnQkFDbEYsTUFBTWEsZ0JBQWdCLE1BQU1GLGtCQUFrQkcsSUFBSTtnQkFFbEQsSUFBSUgsa0JBQWtCSSxFQUFFLElBQUlGLGNBQWNHLE9BQU8sRUFBRTtvQkFDakQsTUFBTXBELFdBQVdpRCxjQUFjakQsUUFBUSxJQUFJaUQsY0FBY2pDLElBQUksRUFBRWhCLFlBQVksRUFBRTtvQkFDN0VHLGdCQUFnQkg7b0JBQ2hCUyxhQUFhTSxPQUFPLENBQUMsaUJBQWlCa0IsS0FBS29CLFNBQVMsQ0FBQ3JEO29CQUVyRCx1RUFBdUU7b0JBQ3ZFLElBQUlBLFNBQVNzRCxNQUFNLEdBQUcsR0FBRzt3QkFDdkIsTUFBTUMsWUFBWTlFLHVEQUFHQSxDQUFDRiw2Q0FBRUEsRUFBRSxTQUFTZSxLQUFLOEMsR0FBRzt3QkFDM0MsTUFBTTFELDZEQUFTQSxDQUFDNkUsV0FBVzs0QkFDekJ2RCxVQUFVQSxTQUFTd0QsR0FBRztpRkFBQyxDQUFDQyxRQUFlQSxNQUFNQyxFQUFFOzt3QkFDakQ7b0JBQ0Y7b0JBQ0EsUUFBUSxPQUFPO2dCQUNqQjtnQkFFQSx3Q0FBd0M7Z0JBQ3hDLE1BQU1DLG1CQUFtQixNQUFNWCxNQUFNLENBQUMsa0NBQWtDLEVBQUUxRCxLQUFLOEMsR0FBRyxFQUFFO2dCQUNwRixNQUFNd0Isa0JBQWtCLE1BQU1ELGlCQUFpQlQsSUFBSTtnQkFFbkQsSUFBSVMsaUJBQWlCUixFQUFFLElBQUlTLGdCQUFnQlIsT0FBTyxFQUFFO29CQUNsRCxNQUFNcEQsV0FBVzRELGdCQUFnQjVELFFBQVEsSUFBSSxFQUFFO29CQUMvQ0csZ0JBQWdCSDtvQkFDaEJTLGFBQWFNLE9BQU8sQ0FBQyxpQkFBaUJrQixLQUFLb0IsU0FBUyxDQUFDckQ7b0JBRXJELElBQUlBLFNBQVNzRCxNQUFNLEdBQUcsR0FBRzt3QkFDdkIsTUFBTUMsWUFBWTlFLHVEQUFHQSxDQUFDRiw2Q0FBRUEsRUFBRSxTQUFTZSxLQUFLOEMsR0FBRzt3QkFDM0MsTUFBTTFELDZEQUFTQSxDQUFDNkUsV0FBVzs0QkFDekJ2RCxVQUFVQSxTQUFTd0QsR0FBRztpRkFBQyxDQUFDQyxRQUFlQSxNQUFNQyxFQUFFOzt3QkFDakQ7b0JBQ0Y7Z0JBQ0YsT0FBTztvQkFDTCxNQUFNRyxNQUFNRCxnQkFBZ0JsRSxLQUFLLElBQUk7b0JBQ3JDLE1BQU0sSUFBSW9FLE1BQU1EO2dCQUNsQjtZQUNGLEVBQUUsT0FBT2YsS0FBSztnQkFDWmpDLFFBQVFuQixLQUFLLENBQUMsK0NBQStDb0Q7Z0JBQzdEekMsU0FBU3lDLGVBQWVnQixRQUFRaEIsSUFBSWlCLE9BQU8sR0FBRztZQUNoRDtRQUNGO3dEQUFHO1FBQUN6RTtLQUFLO0lBRVQsTUFBTU8scUJBQXFCNUIsa0RBQVdBO3dEQUFDLE9BQU8rRixhQUFxQkM7WUFDakUsdUJBQXVCO1lBQ3ZCLHNEQUFzRDtZQUN0RCwyQkFBMkI7WUFFM0JwRCxRQUFRQyxHQUFHLENBQUMsNkNBQTZDLFlBQVk7WUFDckVWLFdBQVc7WUFDWEMsU0FBUztZQUFVLElBQUk7Z0JBQ3JCLHdCQUF3QjtnQkFDeEIsSUFBSSxDQUFDMkQsZUFBZSxPQUFPQSxnQkFBZ0IsVUFBVTtvQkFDbkQsTUFBTSxJQUFJRixNQUFNO2dCQUNsQjtnQkFFQSxJQUFJLENBQUNFLFlBQVlFLFFBQVEsQ0FBQyxNQUFNO29CQUM5QixNQUFNLElBQUlKLE1BQU07Z0JBQ2xCO2dCQUVBLElBQUlFLFlBQVlWLE1BQU0sR0FBRyxJQUFJO29CQUMzQixNQUFNLElBQUlRLE1BQU07Z0JBQ2xCLEVBQU8sOENBQThDO2dCQUNyRGpELFFBQVFDLEdBQUcsQ0FBQyxtREFBbUQsa0JBQWtCO2dCQUVqRixNQUFNcUQsZ0JBQWdCL0Ysb0VBQXFCQSxDQUFDRSwrQ0FBSUEsRUFBRTBGO2dCQUNsRCxNQUFNSSxpQkFBaUIsSUFBSUM7b0VBQVEsQ0FBQ0MsR0FBR0MsU0FDckNDOzRFQUFXLElBQU1ELE9BQU8sSUFBSVQsTUFBTTsyRUFBOEQ7O2dCQUdsRyxNQUFNVyxpQkFBaUIsTUFBTUosUUFBUUssSUFBSSxDQUFDO29CQUFDUDtvQkFBZUM7aUJBQWU7Z0JBQ3pFLE1BQU1PLGVBQWVGLGVBQWVuRixJQUFJO2dCQUN4Q3VCLFFBQVFDLEdBQUcsQ0FBQyx5REFBeUQ2RCxhQUFhdkMsR0FBRztnQkFFckYsd0VBQXdFO2dCQUN4RXZCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDZFQUE2RSxFQUFFeEMsK0NBQUlBLENBQUNzRyxXQUFXLEVBQUV4QyxLQUFLO2dCQUVuSCwyQ0FBMkM7Z0JBQzNDdkIsUUFBUUMsR0FBRyxDQUFDLDhEQUE4RCxxQkFBcUI7Z0JBQy9GLE1BQU02RCxhQUFhckMsVUFBVSxDQUFDO2dCQUM5QnpCLFFBQVFDLEdBQUcsQ0FBQywwQ0FBMEMsb0JBQW9CO2dCQUUxRSxrRUFBa0U7Z0JBQ2xFRCxRQUFRQyxHQUFHLENBQUMsQ0FBQyx5REFBeUQsRUFBRXhDLCtDQUFJQSxDQUFDc0csV0FBVyxFQUFFeEMsS0FBSztnQkFFL0YsZ0ZBQWdGO2dCQUNoRixNQUFNeUMsWUFBWVosaUJBQWlCeEQsYUFBYUMsT0FBTyxDQUFDO2dCQUV4RCxJQUFJbUUsV0FBVztvQkFDYixpREFBaUQ7b0JBQ2pEcEUsYUFBYU0sT0FBTyxDQUFDLGNBQWM4RDtvQkFFbkMsd0VBQXdFO29CQUN4RSxJQUFJcEUsYUFBYUMsT0FBTyxDQUFDLHVCQUF1Qm1FLFdBQVc7d0JBQ3pEaEUsUUFBUUMsR0FBRyxDQUFDO3dCQUNaTCxhQUFhcUUsVUFBVSxDQUFDO29CQUMxQjtvQkFFQSw2Q0FBNkM7b0JBQzdDLElBQUk7d0JBQ0YsTUFBTUMsVUFBVSxNQUFNSixhQUFhckMsVUFBVTt3QkFDN0MsTUFBTTBDLGdCQUFnQixNQUFNaEMsTUFBTSxnQ0FBZ0M7NEJBQ2hFaUMsUUFBUTs0QkFDUkMsU0FBUztnQ0FDUCxnQkFBZ0I7Z0NBQ2hCLGlCQUFpQixDQUFDLE9BQU8sRUFBRUgsU0FBUzs0QkFDdEM7NEJBQ0FJLE1BQU1sRCxLQUFLb0IsU0FBUyxDQUFDO2dDQUFFd0IsV0FBV0E7NEJBQVU7d0JBQzlDO3dCQUVBLElBQUlHLGNBQWM3QixFQUFFLEVBQUU7NEJBQ3BCdEMsUUFBUUMsR0FBRyxDQUFDOzRCQUNaLG9EQUFvRDs0QkFDcEQsTUFBTTZELGFBQWFyQyxVQUFVLENBQUM7d0JBQ2hDLE9BQU87NEJBQ0wsSUFBSThDLGVBQWUsQ0FBQyxRQUFRLEVBQUVKLGNBQWM5RCxNQUFNLEVBQUU7NEJBQ3BELElBQUk7Z0NBQ0YsTUFBTW1FLFlBQVksTUFBTUwsY0FBYzlCLElBQUk7Z0NBQzFDa0MsZUFBZUMsVUFBVTNGLEtBQUssSUFBSTBGOzRCQUNwQyxFQUFFLE9BQU9FLFdBQVc7Z0NBQ2xCLDJDQUEyQztnQ0FDM0MsSUFBSTtvQ0FDRixNQUFNQyxZQUFZLE1BQU1QLGNBQWNRLElBQUk7b0NBQzFDSixlQUFlRyxhQUFhSDtnQ0FDOUIsRUFBRSxPQUFPSyxXQUFXO29DQUNsQjVFLFFBQVFuQixLQUFLLENBQUMsaURBQWlEK0Y7Z0NBQ2pFOzRCQUNGOzRCQUNBNUUsUUFBUW5CLEtBQUssQ0FBQywrQ0FBK0MwRjt3QkFDL0Q7b0JBQ0YsRUFBRSxPQUFPTSxHQUFHO3dCQUNWN0UsUUFBUW5CLEtBQUssQ0FBQywrQ0FBK0NnRztvQkFDL0Q7Z0JBQ0Y7Z0JBRUEsdUNBQXVDO2dCQUN2Q2pGLGFBQWFxRSxVQUFVLENBQUM7Z0JBQ3hCckUsYUFBYXFFLFVBQVUsQ0FBQztnQkFDeEJyRSxhQUFhcUUsVUFBVSxDQUFDO2dCQUN4QnJFLGFBQWFxRSxVQUFVLENBQUM7Z0JBQ3hCckUsYUFBYXFFLFVBQVUsQ0FBQztnQkFFeEIsK0JBQStCO2dCQUMvQnJFLGFBQWFNLE9BQU8sQ0FBQyxhQUFhO2dCQUNsQ1AsWUFBWTtnQkFDWkYsbUJBQW1CO2dCQUVuQixtREFBbUQ7Z0JBQ25ERyxhQUFhcUUsVUFBVSxDQUFDO2dCQUN4QmpFLFFBQVFDLEdBQUcsQ0FBQztZQUF1RSxFQUFFLE9BQU9nQyxLQUFLO2dCQUNqR2pDLFFBQVFuQixLQUFLLENBQUMscURBQXFEb0Q7Z0JBRW5FLElBQUlzQyxlQUFlO2dCQUNuQixJQUFJdEMsZUFBZWdCLE9BQU87b0JBQ3hCLElBQUloQixJQUFJaUIsT0FBTyxDQUFDRyxRQUFRLENBQUMsd0JBQXdCO3dCQUMvQ2tCLGVBQWU7b0JBQ2pCLE9BQU8sSUFBSXRDLElBQUlpQixPQUFPLENBQUNHLFFBQVEsQ0FBQyw4QkFBOEI7d0JBQzVEa0IsZUFBZTtvQkFDakIsT0FBTyxJQUFJdEMsSUFBSWlCLE9BQU8sQ0FBQ0csUUFBUSxDQUFDLCtCQUErQjt3QkFDN0RrQixlQUFlO29CQUNqQixPQUFPLElBQUl0QyxJQUFJaUIsT0FBTyxDQUFDRyxRQUFRLENBQUMsZ0NBQWdDO3dCQUM5RGtCLGVBQWU7b0JBQ2pCLE9BQU87d0JBQ0xBLGVBQWV0QyxJQUFJaUIsT0FBTztvQkFDNUI7Z0JBQ0Y7Z0JBRUExRCxTQUFTK0U7Z0JBQ1RuRixRQUFRO2dCQUNSQyxZQUFZO2dCQUNaQyxnQkFBZ0I7Z0JBQ2hCSyxZQUFZO2dCQUNaRCxrQkFBa0I7Z0JBQ2xCLCtCQUErQjtnQkFDL0JFLGFBQWFxRSxVQUFVLENBQUM7Z0JBQ3hCckUsYUFBYXFFLFVBQVUsQ0FBQztnQkFDeEJyRSxhQUFhcUUsVUFBVSxDQUFDO2dCQUN4QnJFLGFBQWFxRSxVQUFVLENBQUM7Z0JBQ3hCckUsYUFBYXFFLFVBQVUsQ0FBQztnQkFFeEIsdURBQXVEO2dCQUN2RCxNQUFNaEM7WUFDUixTQUFVO2dCQUNSLG1HQUFtRztnQkFDbkdqQyxRQUFRQyxHQUFHLENBQUMsb0RBQW9ELGNBQWM7WUFDaEY7UUFDRjt1REFBRyxFQUFFLEdBQUcscURBQXFEO0lBRTdEL0MsZ0RBQVNBO2tDQUFDO1lBQ1I4QyxRQUFRQyxHQUFHLENBQUMsMERBQTBELHFCQUFxQjtZQUMzRixNQUFNNkUsY0FBY3hILGlFQUFrQkEsQ0FBQ0csK0NBQUlBO3NEQUFFLE9BQU9nQjtvQkFDbEQsc0RBQXNEO29CQUN0RCxNQUFNc0csWUFBWSxJQUFJQyxPQUFPQyxXQUFXO29CQUN4Q2pGLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDBDQUEwQyxFQUFFOEUsVUFBVSxjQUFjLENBQUMsRUFBRXRHLE9BQU87d0JBQUU4QyxLQUFLOUMsS0FBSzhDLEdBQUc7d0JBQUUyRCxPQUFPekcsS0FBS3lHLEtBQUs7b0JBQUMsSUFBSTtvQkFFbEk5RixRQUFRWCxPQUFPLHdCQUF3QjtvQkFFdkMsSUFBSUEsTUFBTTt3QkFDUnVCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHlEQUF5RCxFQUFFeEIsS0FBSzhDLEdBQUcsQ0FBQyxFQUFFLENBQUM7d0JBQ3BGLDRDQUE0Qzt3QkFDNUMsSUFBSTs0QkFDRnZCLFFBQVFDLEdBQUcsQ0FBQzs0QkFDWixNQUFNeEIsS0FBS2dELFVBQVUsQ0FBQzs0QkFDdEJ6QixRQUFRQyxHQUFHLENBQUM7NEJBRVosdUJBQXVCOzRCQUN2QixNQUFNa0YsZ0JBQWdCLE1BQU0xRyxLQUFLa0QsZ0JBQWdCOzRCQUNqRDNCLFFBQVFDLEdBQUcsQ0FBQyxvREFBb0RrRixjQUFjdEQsTUFBTTt3QkFDcEYsdUNBQXVDO3dCQUV6QyxFQUFFLE9BQU9nRCxHQUFHOzRCQUNWN0UsUUFBUW5CLEtBQUssQ0FBQyw4REFBOERnRzt3QkFDOUU7d0JBRUEsZ0VBQWdFO3dCQUNoRSxNQUFNMUQsaUJBQWlCdkIsYUFBYUMsT0FBTyxDQUFDO3dCQUM1QyxJQUFJc0IsZ0JBQWdCOzRCQUNsQjlCLFlBQVkrQixLQUFLQyxLQUFLLENBQUNGO3dCQUN6Qjt3QkFFQSxNQUFNRyxxQkFBcUIxQixhQUFhQyxPQUFPLENBQUM7d0JBQ2hELElBQUl5QixvQkFBb0I7NEJBQ3RCaEMsZ0JBQWdCOEIsS0FBS0MsS0FBSyxDQUFDQzt3QkFDN0I7d0JBRUEsMkJBQTJCO3dCQUMzQnRCLFFBQVFDLEdBQUcsQ0FBQzt3QkFDWixNQUFNbkI7d0JBQ05rQixRQUFRQyxHQUFHLENBQUM7d0JBQ1pSLG1CQUFtQjt3QkFDbkJPLFFBQVFDLEdBQUcsQ0FBQzt3QkFFWixpREFBaUQ7d0JBQ2pELElBQUl2QixVQUFVcUIsU0FBUyxZQUFZcUIsS0FBS0MsS0FBSyxDQUFDRixrQkFBa0IsT0FBT3BCLFNBQVMsVUFBVTs0QkFDeEYsTUFBTWhCO3dCQUNSO29CQUVGLE9BQU87d0JBQ0xpQixRQUFRQyxHQUFHLENBQUM7d0JBQ1osNENBQTRDO3dCQUM1Q1osWUFBWTt3QkFDWkMsZ0JBQWdCO3dCQUNoQkssWUFBWTt3QkFDWkQsa0JBQWtCO3dCQUNsQkQsbUJBQW1CO3dCQUNuQkcsYUFBYXFFLFVBQVUsQ0FBQzt3QkFDeEJyRSxhQUFhcUUsVUFBVSxDQUFDO3dCQUN4QnJFLGFBQWFxRSxVQUFVLENBQUMsd0JBQXdCLHVDQUF1Qzt3QkFDdkZyRSxhQUFhcUUsVUFBVSxDQUFDO3dCQUN4QnJFLGFBQWFxRSxVQUFVLENBQUM7d0JBQ3hCakUsUUFBUUMsR0FBRyxDQUFDO29CQUNkO29CQUVBRCxRQUFRQyxHQUFHLENBQUMsQ0FBQyw4REFBOEQsRUFBRSxJQUFJK0UsT0FBT0MsV0FBVyxHQUFHLENBQUMsQ0FBQztvQkFDeEcxRixXQUFXO2dCQUNiOztZQUVBLG1CQUFtQjtZQUNuQjswQ0FBTztvQkFDTFMsUUFBUUMsR0FBRyxDQUFDLDJEQUEyRCxjQUFjO29CQUNyRjZFO2dCQUNGOztRQUNGO2lDQUFHO1FBQUNoRztRQUFpQkM7S0FBb0I7SUFFekM3QixnREFBU0E7a0NBQUM7WUFDUix3Q0FBd0M7WUFDeEMsSUFBSTBCLFdBQVd5QixXQUFXLFdBQVc7WUFFckMsa0VBQWtFO1lBQ2xFLE1BQU0rRSxjQUFjaEYsU0FBUzNCO1lBQzdCLE1BQU1ULGtCQUFrQixDQUFFLENBQUNTLFFBQVEsQ0FBQyxDQUFDQSxLQUFLOEMsR0FBRyxJQUFLbEIsV0FBVztZQUU3RCxJQUFJckMsaUJBQWlCO2dCQUNuQix5RUFBeUU7Z0JBQ3pFLElBQUksQ0FBQ0UsVUFBVTtvQkFDYjhCLFFBQVFDLEdBQUcsQ0FBQztvQkFFWixnRUFBZ0U7b0JBQ2hFLElBQUlJLFdBQVcsbUJBQW1CK0UsYUFBYXJGLE1BQU07d0JBQ25EQyxRQUFRQyxHQUFHLENBQUMsQ0FBQyw4Q0FBOEMsRUFBRW1GLFlBQVlyRixJQUFJLEVBQUU7d0JBRS9FLGtFQUFrRTt3QkFDbEUsTUFBTXNGO29FQUFnQjtnQ0FDcEIsSUFBSTtvQ0FDRnJGLFFBQVFDLEdBQUcsQ0FBQztvQ0FDWixNQUFNcUYsV0FBVyxNQUFNbkQsTUFBTSwyQkFBMkI7d0NBQ3REaUMsUUFBUTt3Q0FDUm1CLGFBQWE7d0NBQ2JsQixTQUFTOzRDQUNQLGdCQUFnQjt3Q0FDbEI7b0NBQ0Y7b0NBRUFyRSxRQUFRQyxHQUFHLENBQUMsc0NBQXNDcUYsU0FBU2pGLE1BQU07b0NBRWpFLElBQUlpRixTQUFTaEQsRUFBRSxFQUFFO3dDQUNmLE1BQU1rRCxXQUFXLE1BQU1GLFNBQVNqRCxJQUFJO3dDQUNwQyxJQUFJbUQsU0FBU2pELE9BQU8sRUFBRTs0Q0FDcEJ2QyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxpQ0FBaUMsRUFBRXVGLFNBQVN6RixJQUFJLEVBQUUsRUFBRXlGOzRDQUNqRTdGLFlBQVk2RixTQUFTekYsSUFBSTs0Q0FDekJILGFBQWFNLE9BQU8sQ0FBQyxhQUFhc0YsU0FBU3pGLElBQUk7NENBRS9DLHdEQUF3RDs0Q0FDeEQsSUFBSXlGLFNBQVN6RixJQUFJLEtBQUssUUFBUTtnREFDNUIscUZBQXFGO2dEQUNyRixNQUFNMEYsaUJBQWlCN0YsYUFBYUMsT0FBTyxDQUFDO2dEQUM1QyxNQUFNNkYsZ0JBQWdCRCxrQkFBa0I7Z0RBQ3hDbEgsbUJBQW1CbUg7Z0RBQ25COUYsYUFBYU0sT0FBTyxDQUFDLHFCQUFxQndGO2dEQUMxQzFGLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLCtCQUErQixFQUFFeUYsY0FBYyxtQkFBbUIsQ0FBQzs0Q0FDbEYsT0FBTyxJQUFJRixTQUFTekYsSUFBSSxLQUFLLFlBQVl5RixTQUFTekYsSUFBSSxLQUFLLFlBQVk7Z0RBQ3JFeEIsbUJBQW1CaUgsU0FBU3pGLElBQUk7Z0RBQ2hDSCxhQUFhTSxPQUFPLENBQUMscUJBQXFCc0YsU0FBU3pGLElBQUk7NENBQ3pEOzRDQUNBO3dDQUNGLE9BQU87NENBQ0xDLFFBQVFDLEdBQUcsQ0FBQyxxREFBcUR1Rjt3Q0FDbkU7b0NBQ0YsT0FBTzt3Q0FDTHhGLFFBQVFDLEdBQUcsQ0FBQyw0Q0FBNENxRixTQUFTakYsTUFBTTtvQ0FDekU7b0NBRUEsd0NBQXdDO29DQUN4Q0wsUUFBUUMsR0FBRyxDQUFDO29DQUNaLElBQUltRixhQUFhckYsTUFBTTt3Q0FDckJKLFlBQVl5RixZQUFZckYsSUFBSTt3Q0FDNUJILGFBQWFNLE9BQU8sQ0FBQyxhQUFha0YsWUFBWXJGLElBQUk7d0NBRWxELHFDQUFxQzt3Q0FDckMsSUFBSXFGLFlBQVlyRixJQUFJLEtBQUssUUFBUTs0Q0FDL0IsTUFBTTBGLGlCQUFpQjdGLGFBQWFDLE9BQU8sQ0FBQzs0Q0FDNUMsTUFBTTZGLGdCQUFnQkQsa0JBQWtCOzRDQUN4Q2xILG1CQUFtQm1IOzRDQUNuQjlGLGFBQWFNLE9BQU8sQ0FBQyxxQkFBcUJ3Rjt3Q0FDNUMsT0FBTyxJQUFJTixZQUFZckYsSUFBSSxLQUFLLFlBQVlxRixZQUFZckYsSUFBSSxLQUFLLFlBQVk7NENBQzNFeEIsbUJBQW1CNkcsWUFBWXJGLElBQUk7NENBQ25DSCxhQUFhTSxPQUFPLENBQUMscUJBQXFCa0YsWUFBWXJGLElBQUk7d0NBQzVEO29DQUNGO2dDQUNGLEVBQUUsT0FBT2xCLE9BQU87b0NBQ2RtQixRQUFRbkIsS0FBSyxDQUFDLDJDQUEyQ0E7b0NBQ3pELDRDQUE0QztvQ0FDNUMsSUFBSXVHLGFBQWFyRixNQUFNO3dDQUNyQkMsUUFBUUMsR0FBRyxDQUFDO3dDQUNaTixZQUFZeUYsWUFBWXJGLElBQUk7d0NBQzVCSCxhQUFhTSxPQUFPLENBQUMsYUFBYWtGLFlBQVlyRixJQUFJO3dDQUVsRCxtQ0FBbUM7d0NBQ25DLElBQUlxRixZQUFZckYsSUFBSSxLQUFLLFFBQVE7NENBQy9CLE1BQU0wRixpQkFBaUI3RixhQUFhQyxPQUFPLENBQUM7NENBQzVDLE1BQU02RixnQkFBZ0JELGtCQUFrQjs0Q0FDeENsSCxtQkFBbUJtSDs0Q0FDbkI5RixhQUFhTSxPQUFPLENBQUMscUJBQXFCd0Y7d0NBQzVDLE9BQU8sSUFBSU4sWUFBWXJGLElBQUksS0FBSyxZQUFZcUYsWUFBWXJGLElBQUksS0FBSyxZQUFZOzRDQUMzRXhCLG1CQUFtQjZHLFlBQVlyRixJQUFJOzRDQUNuQ0gsYUFBYU0sT0FBTyxDQUFDLHFCQUFxQmtGLFlBQVlyRixJQUFJO3dDQUM1RDtvQ0FDRjtnQ0FDRjs0QkFDRjs7d0JBRUFzRjt3QkFDQTtvQkFDRjtvQkFFQSxpQ0FBaUM7b0JBQ2pDLE1BQU1NLGFBQWEvRixhQUFhQyxPQUFPLENBQUM7b0JBRXhDLElBQUk4RixZQUFZO3dCQUNkM0YsUUFBUUMsR0FBRyxDQUFDLENBQUMsMENBQTBDLEVBQUUwRixZQUFZO3dCQUNyRWhHLFlBQVlnRzt3QkFFWiw2Q0FBNkM7d0JBQzdDLElBQUlBLGVBQWUsV0FBVzs0QkFDNUIsTUFBTUMsa0JBQWtCaEcsYUFBYUMsT0FBTyxDQUFDLGlCQUFpQkQsYUFBYUMsT0FBTyxDQUFDOzRCQUNuRixJQUFJK0YsaUJBQWlCO2dDQUNuQjVGLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGdEQUFnRCxFQUFFMkYsaUJBQWlCO2dDQUNoRmxHLGtCQUFrQmtHOzRCQUNwQjt3QkFDRjtvQkFDRixPQUFPLElBQUluSCxNQUFNO3dCQUNmLCtCQUErQjt3QkFDL0IsOEJBQThCO3dCQUM5QixNQUFNTCxXQUFXd0IsYUFBYUMsT0FBTyxDQUFDLGdCQUFnQkQsYUFBYUMsT0FBTyxDQUFDO3dCQUUzRSx5RUFBeUU7d0JBQ3pFLE1BQU1nRyxxQkFBcUJqRyxhQUFhQyxPQUFPLENBQUM7d0JBRWhELElBQUl6QixVQUFVOzRCQUNaNEIsUUFBUUMsR0FBRyxDQUFDOzRCQUNaTixZQUFZOzRCQUVaLG9FQUFvRTs0QkFDcEUsSUFBSWtHLG9CQUFvQjtnQ0FDdEI3RixRQUFRQyxHQUFHLENBQUMsQ0FBQyxvREFBb0QsRUFBRTRGLG9CQUFvQjtnQ0FDdkZuRyxrQkFBa0JtRzs0QkFDcEI7d0JBQ0YsT0FBTzs0QkFDTCxpREFBaUQ7NEJBQ2pEN0YsUUFBUUMsR0FBRyxDQUFDOzRCQUNaTixZQUFZOzRCQUVaLHlCQUF5Qjs0QkFDekIsTUFBTWlHLGtCQUFrQmhHLGFBQWFDLE9BQU8sQ0FBQyxpQkFBaUJELGFBQWFDLE9BQU8sQ0FBQzs0QkFDbkYsSUFBSStGLGlCQUFpQjtnQ0FDbkI1RixRQUFRQyxHQUFHLENBQUMsQ0FBQyxnQ0FBZ0MsRUFBRTJGLGlCQUFpQjtnQ0FDaEVsRyxrQkFBa0JrRzs0QkFDcEIsT0FBTztnQ0FDTDVGLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDhDQUE4QyxFQUFFeEIsS0FBSzhDLEdBQUcsRUFBRTtnQ0FDdkU3QixrQkFBa0JqQixLQUFLOEMsR0FBRztnQ0FDMUIzQixhQUFhTSxPQUFPLENBQUMsY0FBY3pCLEtBQUs4QyxHQUFHO2dDQUMzQzNCLGFBQWFNLE9BQU8sQ0FBQyxtQkFBbUJ6QixLQUFLOEMsR0FBRzs0QkFDbEQ7d0JBQ0Y7b0JBQ0Y7Z0JBQ0Y7WUFDQSxrQ0FBa0M7WUFDcEM7UUFDRjtpQ0FBRztRQUFDM0M7UUFBU0g7UUFBTVA7UUFBVXlCO1FBQWFEO1FBQW1CVztRQUFRRDtLQUFRO0lBRTNFLDhCQUE4QjtJQUM5QixvREFBb0Q7SUFDcEQsOEJBQThCO0lBRTlCLDhCQUE4QjtJQUM5QiwwQ0FBMEM7SUFDMUMscUZBQXFGO0lBQ3JGLDhCQUE4QjtJQUM5QmxELGdEQUFTQTtrQ0FBQztZQUNSLGtFQUFrRTtZQUNsRSxJQUFJMEIsV0FBV3lCLFdBQVcsV0FBVztnQkFDbkNMLFFBQVFDLEdBQUcsQ0FBQyxxREFBcUQ7b0JBQUVyQjtvQkFBU3lCO2dCQUFPO2dCQUNuRjtZQUNGO1lBRUEsTUFBTVMsY0FBYyxNQUE2QixHQUFHQyxDQUF3QixHQUFHO1lBQy9FLE1BQU1KLGNBQWVQLFNBQVMzQixNQUFzQnNCO1lBQ3BELE1BQU0rRiwwQkFBMEJ6RixXQUFXLG1CQUFtQixDQUFDLENBQUNNO1lBQ2hFLE1BQU1vRixxQkFBcUIsQ0FBQyxDQUFDM0YsV0FBVyxDQUFDLENBQUVBLFNBQWlCM0IsTUFBTXNCO1lBRWxFQyxRQUFRQyxHQUFHLENBQUMsMkJBQTJCO2dCQUNyQ2E7Z0JBQ0FsQztnQkFDQXlCO2dCQUNBeUY7Z0JBQ0FDO2dCQUNBcEY7Z0JBQ0FyQztnQkFDQThCLFNBQVMsQ0FBQyxDQUFDQTtnQkFDWGdGLGFBQWFoRixTQUFTM0I7Z0JBQ3RCdUgsY0FBYyxDQUFDLENBQUN2SDtnQkFDaEJ3SCxpQkFBa0I3RixTQUFTM0IsTUFBY3NCO2dCQUN6Q21HLFlBQVk5RjtZQUNkO1lBRUEsMENBQTBDO1lBQzFDLGlEQUFpRDtZQUNqRCxNQUFNK0Ysd0JBQXdCTCwyQkFBMkJDO1lBRXpEL0YsUUFBUUMsR0FBRyxDQUFDLDRDQUE0QztnQkFDdEQ2RjtnQkFDQUM7Z0JBQ0FJO2dCQUNBQyxhQUFhL0YsV0FBVztnQkFDeEJnRyxrQkFBa0IsQ0FBQyxDQUFDMUY7Z0JBQ3BCMkYsaUJBQWlCLENBQUMsQ0FBQ2xHO2dCQUNuQm1HLHNCQUFzQixDQUFDLENBQUVuRyxTQUFpQjNCLE1BQU1zQjtZQUNsRDtZQUVBLG9GQUFvRjtZQUNwRixvRUFBb0U7WUFDcEUsSUFBSSxDQUFDZSxZQUFZSSxVQUFVLENBQUMsZ0JBQWdCSixnQkFBZ0IsZ0JBQWdCQSxZQUFZSSxVQUFVLENBQUMsY0FBYSxLQUFNZCxXQUFZQSxTQUFpQjNCLE1BQU1zQixNQUFNO2dCQUM3Six3Q0FBd0M7Z0JBQ3hDLElBQUlZLGdCQUFnQixRQUFRO29CQUMxQlgsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLHlEQUF5RDtnQkFDM0QsT0FBTztvQkFDTEQsUUFBUUMsR0FBRyxDQUFDO29CQUNaO2dCQUNGO1lBQ0Y7WUFFQSxJQUFJLENBQUNrRyx1QkFBdUI7Z0JBQzFCLE1BQU1LLGNBQWM7b0JBQUM7b0JBQVU7b0JBQWE7b0JBQW9CO2lCQUFTO2dCQUN6RSw4Q0FBOEM7Z0JBQzlDLElBQUksQ0FBQ0EsWUFBWUMsSUFBSTs4Q0FBQ0MsQ0FBQUEsSUFBSzVGLFlBQVlJLFVBQVUsQ0FBQ3dGOzhDQUFLO29CQUNyRDFHLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHNEQUFzRCxFQUFFYSxZQUFZLFVBQVUsQ0FBQztvQkFDNUZkLFFBQVFDLEdBQUcsQ0FBQyxtRUFBbUU7d0JBQzdFYTt3QkFDQWdGO3dCQUNBQzt3QkFDQTNGLFNBQVNBO3dCQUNUZ0YsYUFBYWhGLFNBQVMzQjt3QkFDdEI0Qjt3QkFDQU07b0JBQ0Y7b0JBQ0FKLE9BQU9vRyxPQUFPLENBQUM7Z0JBQ2pCLE9BQU87b0JBQ0wzRyxRQUFRQyxHQUFHLENBQUM7Z0JBQ2Q7Z0JBQ0E7WUFDRjtZQUVBRCxRQUFRQyxHQUFHLENBQUM7WUFFWix3Q0FBd0M7WUFFeEMsdURBQXVEO1lBQ3ZELElBQUlhLFlBQVlJLFVBQVUsQ0FBQyxXQUFXO2dCQUNwQyx5RUFBeUU7Z0JBQ3pFLE1BQU0wRixjQUFjakcsZ0JBQWdCLFNBQy9CckMsb0JBQW9CLGFBQWEsd0JBQXdCLGVBQzFELGNBQWMsc0JBQXNCO2dCQUV4QzBCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDhEQUE4RCxFQUFFMkcsYUFBYTtnQkFDMUZyRyxPQUFPb0csT0FBTyxDQUFDQztnQkFDZjtZQUNGO1lBRUEseUNBQXlDO1lBQ3pDLHlFQUF5RTtZQUN6RSxJQUFJakcsZ0JBQWdCLFFBQVE7Z0JBQzFCLHNGQUFzRjtnQkFDdEYsSUFBSXJDLG9CQUFvQixjQUFjLENBQUN3QyxZQUFZSSxVQUFVLENBQUMsY0FBYztvQkFDMUVsQixRQUFRQyxHQUFHLENBQUM7b0JBQ1pNLE9BQU9vRyxPQUFPLENBQUM7b0JBQ2YsUUFBUSwyQ0FBMkM7Z0JBQ3JEO2dCQUNBLElBQUlySSxvQkFBb0IsWUFBWXdDLFlBQVlJLFVBQVUsQ0FBQyxjQUFjO29CQUN2RWxCLFFBQVFDLEdBQUcsQ0FBQztvQkFDWk0sT0FBT29HLE9BQU8sQ0FBQztvQkFDZixRQUFRLDJDQUEyQztnQkFDckQ7Z0JBRUEsdUZBQXVGO2dCQUN2RjNHLFFBQVFDLEdBQUcsQ0FBQztnQkFDWjtZQUNGO1lBRUEsb0RBQW9EO1lBQ3BELDhEQUE4RDtZQUM5RCxJQUFJVSxnQkFBZ0IsWUFBWUcsWUFBWUksVUFBVSxDQUFDLGNBQWM7Z0JBQ25FbEIsUUFBUUMsR0FBRyxDQUFDLENBQUMscUVBQXFFLENBQUM7Z0JBQ25GTSxPQUFPb0csT0FBTyxDQUFDO1lBQ2pCO1FBRUEsOEZBQThGO1FBQzlGLGtDQUFrQztRQUVwQztpQ0FBRztRQUFDL0g7UUFBU3lCO1FBQVFEO1FBQVM5QjtRQUFpQmlDO0tBQU8sR0FBRyxxQ0FBcUM7SUFFbEcscUJBQ0UsOERBQUN4QyxZQUFZOEksUUFBUTtRQUNuQkMsT0FBTztZQUNMckk7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQWIsaUJBQWlCLENBQUUsQ0FBQ1MsUUFBUSxDQUFDLENBQUNBLEtBQUs4QyxHQUFHLElBQUtsQixXQUFXLG1CQUFvQixDQUFDLENBQUNELFdBQVcsQ0FBQyxDQUFFQSxTQUFpQjNCLE1BQU1zQjtZQUNqSCxxQ0FBcUM7WUFDckM5QixnQkFBZ0JBLGtCQUFtQixVQUFVUSxNQUFzQnNCLFNBQVMsWUFBYUssU0FBUzNCLE1BQXNCb0UsS0FBSyxJQUFHO1lBQ2hJM0UsVUFBVSxTQUFVTyxNQUFzQnNCLFFBQVFyQixVQUFVcUIsUUFBUTdCLFlBQVk7WUFDaEZDLG9CQUFvQnNDO1lBQ3BCckMsVUFBVSxDQUFDLFNBQVVLLE1BQXNCc0IsUUFBUXJCLFVBQVVxQixRQUFRN0IsUUFBTyxNQUFPLFlBQ3pFLENBQUMsU0FBVU8sTUFBc0JzQixRQUFRckIsVUFBVXFCLFFBQVE3QixRQUFPLE1BQU87WUFDbkZHLFlBQVksQ0FBQyxTQUFVSSxNQUFzQnNCLFFBQVFyQixVQUFVcUIsUUFBUTdCLFFBQU8sTUFBTyxjQUN6RSxDQUFDLFNBQVVPLE1BQXNCc0IsUUFBUXJCLFVBQVVxQixRQUFRN0IsUUFBTyxNQUFPO1lBQ3JGSTtZQUNBQyxvQkFBb0J1QjtZQUNwQnRCO1lBQ0FNO1lBQ0FDO1lBQ0FDO1lBQTJCQyxRQUFRO2dCQUMvQmUsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLElBQUk7b0JBQ0YsTUFBTXpDLHNEQUFPQSxDQUFDQywrQ0FBSUE7b0JBRWxCLG1DQUFtQztvQkFDbkMsSUFBSTt3QkFDRixNQUFNc0osZ0JBQWdCLE1BQU01RSxNQUFNLGdDQUFnQzs0QkFDaEVpQyxRQUFROzRCQUNSQyxTQUFTO2dDQUNQLGdCQUFnQjs0QkFDbEI7d0JBQ0Y7d0JBQ0EsSUFBSTBDLGNBQWN6RSxFQUFFLEVBQUU7NEJBQ3BCdEMsUUFBUUMsR0FBRyxDQUFDO3dCQUNkLE9BQU87NEJBQ0xELFFBQVFnSCxJQUFJLENBQUM7d0JBQ2Y7b0JBQ0YsRUFBRSxPQUFPQyxZQUFZO3dCQUNuQmpILFFBQVFnSCxJQUFJLENBQUMsZ0RBQWdEQztvQkFDN0QsaURBQWlEO29CQUNuRDtvQkFFQSxnQ0FBZ0M7b0JBQ2hDN0gsUUFBUTtvQkFDUkMsWUFBWTtvQkFDWkMsZ0JBQWdCO29CQUNoQkssWUFBWTtvQkFDWkQsa0JBQWtCO29CQUNsQm5CLG1CQUFtQjtvQkFDbkJrQixtQkFBbUI7b0JBRW5CLDRCQUE0QjtvQkFDNUJHLGFBQWFzSCxLQUFLO29CQUNsQkMsZUFBZUQsS0FBSztvQkFFcEIsZ0RBQWdEO29CQUNoRCxJQUFJO3dCQUNGLElBQUksZUFBZW5HLFFBQVE7NEJBQ3pCLE1BQU1xRyxZQUFZLE1BQU1DLFVBQVVELFNBQVM7NEJBQzNDQSxVQUFVRSxPQUFPLENBQUMsT0FBTzVKO2dDQUN2QixJQUFJQSxHQUFHNkosSUFBSSxFQUFFO29DQUNYRixVQUFVRyxjQUFjLENBQUM5SixHQUFHNkosSUFBSTtnQ0FDbEM7NEJBQ0Y7d0JBQ0Y7b0JBQ0YsRUFBRSxPQUFPRSxVQUFVO3dCQUNqQnpILFFBQVFnSCxJQUFJLENBQUMsMkNBQTJDUztvQkFDMUQ7b0JBRUEsOEJBQThCO29CQUM5QixJQUFJO3dCQUNGLElBQUksWUFBWTFHLFFBQVE7NEJBQ3RCLE1BQU0yRyxhQUFhLE1BQU1DLE9BQU9DLElBQUk7NEJBQ3BDLE1BQU1wRSxRQUFRcUUsR0FBRyxDQUNmSCxXQUFXL0UsR0FBRyxDQUFDbUYsQ0FBQUEsWUFBYUgsT0FBT0ksTUFBTSxDQUFDRDs0QkFFNUM5SCxRQUFRQyxHQUFHLENBQUM7d0JBQ2Q7b0JBQ0YsRUFBRSxPQUFPK0gsU0FBUzt3QkFDaEJoSSxRQUFRZ0gsSUFBSSxDQUFDLHVEQUF1RGdCO29CQUN0RTtvQkFFQWhJLFFBQVFDLEdBQUcsQ0FBQztvQkFDWk0sT0FBTzBILElBQUksQ0FBQztnQkFDZCxFQUFFLE9BQU9oRyxLQUFLO29CQUNaakMsUUFBUW5CLEtBQUssQ0FBQyxnQ0FBZ0NvRDtvQkFDOUN6QyxTQUFTO29CQUNULDZEQUE2RDtvQkFDN0RKLFFBQVE7b0JBQ1JDLFlBQVk7b0JBQ1pDLGdCQUFnQjtvQkFDaEJLLFlBQVk7b0JBQ1pELGtCQUFrQjtvQkFDbEJuQixtQkFBbUI7b0JBQ25Ca0IsbUJBQW1CO29CQUNuQkcsYUFBYXNILEtBQUs7b0JBQ2xCQyxlQUFlRCxLQUFLO29CQUNwQjNHLE9BQU8wSCxJQUFJLENBQUMsV0FBVyx5QkFBeUI7Z0JBQ2xEO1lBQ0Y7UUFDRjtrQkFFQzlJOzs7Ozs7QUFHUDtBQUVPLE1BQU0rSSxVQUFVLElBQU1qTCxpREFBVUEsQ0FBQ2MsYUFBYSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcc3JjXFxhcHBcXHByb3ZpZGVyc1xcQXV0aFByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlLCB1c2VDYWxsYmFjaywgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgVXNlciwgb25BdXRoU3RhdGVDaGFuZ2VkLCBzaWduSW5XaXRoQ3VzdG9tVG9rZW4sIHNpZ25PdXQgfSBmcm9tICdmaXJlYmFzZS9hdXRoJztcbmltcG9ydCB7IGF1dGgsIGRiIH0gZnJvbSAnQC9saWIvZmlyZWJhc2UnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IGRvYywgZ2V0RG9jLCB1cGRhdGVEb2MgfSBmcm9tICdmaXJlYmFzZS9maXJlc3RvcmUnO1xuaW1wb3J0IHsgdXNlU2Vzc2lvbiB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XG5pbXBvcnQgeyBTZXNzaW9uIH0gZnJvbSAnbmV4dC1hdXRoJzsgLy8gSW1wb3J0IFNlc3Npb24gdHlwZVxuXG4vLyBEZWZpbmUgYW4gaW50ZXJmYWNlIGZvciB0aGUgZXhwZWN0ZWQgdXNlciBzdHJ1Y3R1cmUgd2l0aGluIHRoZSBOZXh0QXV0aCBzZXNzaW9uXG5pbnRlcmZhY2UgU2Vzc2lvblVzZXIge1xuICBpZD86IHN0cmluZzsgLy8gQXNzdW1pbmcgJ2lkJyBob2xkcyB0aGUgc3R1ZGVudCBJRCBmb3Igc3R1ZGVudHNcbiAgcm9sZT86IHN0cmluZztcbiAgbmFtZT86IHN0cmluZyB8IG51bGw7XG4gIGVtYWlsPzogc3RyaW5nIHwgbnVsbDtcbiAgaW1hZ2U/OiBzdHJpbmcgfCBudWxsO1xufVxuXG4vLyBFeHRlbmQgdGhlIE5leHRBdXRoIFNlc3Npb24gdHlwZSBpZiBuZWVkZWQsIG9yIHVzZSBTZXNzaW9uVXNlciBkaXJlY3RseVxuaW50ZXJmYWNlIEV4dGVuZGVkU2Vzc2lvbiBleHRlbmRzIE9taXQ8U2Vzc2lvbiwgJ3VzZXInPiB7XG4gIHVzZXI/OiBTZXNzaW9uVXNlcjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBBdXRoQ29udGV4dFR5cGUge1xuICB1c2VyOiBVc2VyIHwgbnVsbDtcbiAgdXNlckRhdGE6IGFueSB8IG51bGw7XG4gIGNoaWxkcmVuRGF0YTogYW55W10gfCBudWxsO1xuICBsb2FkaW5nOiBib29sZWFuO1xuICBlcnJvcjogc3RyaW5nIHwgbnVsbDtcbiAgaXNBdXRoZW50aWNhdGVkOiBib29sZWFuO1xuICBzdHVkZW50U2Vzc2lvbjogYW55IHwgbnVsbDtcbiAgdXNlclJvbGU6IHN0cmluZyB8IG51bGw7XG4gIGxvY2FsRWZmZWN0aXZlUm9sZTogc3RyaW5nIHwgbnVsbDsgLy8gVGhlIHJvbGUgYmVpbmcgdXNlZCBpbiB0aGUgY3VycmVudCBjb250ZXh0XG4gIGlzUGFyZW50OiBib29sZWFuO1xuICBpc09wZXJhdG9yOiBib29sZWFuO1xuICBjdXJyZW50Vmlld1JvbGU6ICdwYXJlbnQnIHwgJ29wZXJhdG9yJyB8IG51bGw7XG4gIHNldEN1cnJlbnRWaWV3Um9sZTogKHJvbGU6ICdwYXJlbnQnIHwgJ29wZXJhdG9yJykgPT4gdm9pZDtcbiAgbWFudWFsU3luY1dpdGhMb2NhbFN0b3JhZ2U6ICgpID0+IHZvaWQ7XG4gIHJlZnJlc2hVc2VyRGF0YTogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgcmVmcmVzaENoaWxkcmVuRGF0YTogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgaGFuZGxlTG9naW5TdWNjZXNzOiAoY3VzdG9tVG9rZW46IHN0cmluZywgc3R1ZGVudElkSGludD86IHN0cmluZykgPT4gUHJvbWlzZTx2b2lkPjtcbiAgbG9nb3V0OiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xufVxuXG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8QXV0aENvbnRleHRUeXBlPih7XG4gIGlzQXV0aGVudGljYXRlZDogZmFsc2UsXG4gIHN0dWRlbnRTZXNzaW9uOiBudWxsLFxuICB1c2VyUm9sZTogbnVsbCxcbiAgbG9jYWxFZmZlY3RpdmVSb2xlOiBudWxsLFxuICBpc1BhcmVudDogZmFsc2UsXG4gIGlzT3BlcmF0b3I6IGZhbHNlLFxuICBjdXJyZW50Vmlld1JvbGU6IG51bGwsXG4gIHNldEN1cnJlbnRWaWV3Um9sZTogKCkgPT4ge30sXG4gIG1hbnVhbFN5bmNXaXRoTG9jYWxTdG9yYWdlOiAoKSA9PiB7fSxcbiAgdXNlcjogbnVsbCxcbiAgdXNlckRhdGE6IG51bGwsXG4gIGNoaWxkcmVuRGF0YTogbnVsbCxcbiAgbG9hZGluZzogdHJ1ZSxcbiAgZXJyb3I6IG51bGwsXG4gIHJlZnJlc2hVc2VyRGF0YTogYXN5bmMgKCkgPT4ge30sXG4gIHJlZnJlc2hDaGlsZHJlbkRhdGE6IGFzeW5jICgpID0+IHt9LFxuICBoYW5kbGVMb2dpblN1Y2Nlc3M6IGFzeW5jICgpID0+IHt9LFxuICBsb2dvdXQ6IGFzeW5jICgpID0+IHt9LFxufSk7XG5cbmV4cG9ydCBmdW5jdGlvbiBBdXRoUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxVc2VyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFt1c2VyRGF0YSwgc2V0VXNlckRhdGFdID0gdXNlU3RhdGU8YW55IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtjaGlsZHJlbkRhdGEsIHNldENoaWxkcmVuRGF0YV0gPSB1c2VTdGF0ZTxhbnlbXSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2lzQXV0aGVudGljYXRlZCwgc2V0SXNBdXRoZW50aWNhdGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3N0dWRlbnRTZXNzaW9uLCBzZXRTdHVkZW50U2Vzc2lvbl0gPSB1c2VTdGF0ZTxhbnkgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3VzZXJSb2xlLCBzZXRVc2VyUm9sZV0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgXG4gIC8vIEluaXRpYWxpemUgY3VycmVudFZpZXdSb2xlIGZyb20gbG9jYWxTdG9yYWdlXG4gIGNvbnN0IFtjdXJyZW50Vmlld1JvbGUsIHNldEN1cnJlbnRWaWV3Um9sZV0gPSB1c2VTdGF0ZTwncGFyZW50JyB8ICdvcGVyYXRvcicgfCBudWxsPigoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICByZXR1cm4gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2N1cnJlbnRfdmlld19yb2xlJykgYXMgJ3BhcmVudCcgfCAnb3BlcmF0b3InIHwgbnVsbDtcbiAgICB9XG4gICAgcmV0dXJuIG51bGw7XG4gIH0pO1xuXG4gIC8vIEVuaGFuY2VkIHNldEN1cnJlbnRWaWV3Um9sZSB0aGF0IHBlcnNpc3RzIHRoZSBzZWxlY3Rpb25cbiAgY29uc3QgdXBkYXRlQ3VycmVudFZpZXdSb2xlID0gdXNlQ2FsbGJhY2soKHJvbGU6ICdwYXJlbnQnIHwgJ29wZXJhdG9yJykgPT4ge1xuICAgIGNvbnNvbGUubG9nKGBBdXRoUHJvdmlkZXI6IFVwZGF0aW5nIGN1cnJlbnQgdmlldyByb2xlIGZyb20gJHtjdXJyZW50Vmlld1JvbGV9IHRvOiAke3JvbGV9YCk7XG4gICAgc2V0Q3VycmVudFZpZXdSb2xlKHJvbGUpO1xuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdjdXJyZW50X3ZpZXdfcm9sZScsIHJvbGUpO1xuICAgIGNvbnNvbGUubG9nKGBBdXRoUHJvdmlkZXI6IFVwZGF0ZWQgY3VycmVudCB2aWV3IHJvbGUgdG86ICR7cm9sZX1gKTtcbiAgfSwgW2N1cnJlbnRWaWV3Um9sZV0pO1xuICBjb25zdCB7IGRhdGE6IHNlc3Npb24sIHN0YXR1cyB9ID0gdXNlU2Vzc2lvbigpOyAvLyBzZXNzaW9uIHdpbGwgYmUgb2YgdHlwZSBTZXNzaW9uIHwgbnVsbFxuXG4gIC8vIFN5bmNocm9uaXplIEF1dGhQcm92aWRlciBhdXRoIHN0YXRlIHdpdGggTmV4dEF1dGggc2Vzc2lvblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzdGF0dXMgPT09ICdsb2FkaW5nJykgcmV0dXJuOyAvLyBrZWVwIGV4aXN0aW5nIGxvYWRpbmcgc3RhdGUgdW50aWwgc2Vzc2lvbiBrbm93blxuXG4gICAgaWYgKHN0YXR1cyA9PT0gJ2F1dGhlbnRpY2F0ZWQnICYmIHNlc3Npb24/LnVzZXIpIHtcbiAgICAgIC8vIE1hcmsgYXMgYXV0aGVudGljYXRlZFxuICAgICAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0F1dGhQcm92aWRlcjogTmV4dEF1dGggc2Vzc2lvbiBkZXRlY3RlZCDigJMgbWFya2luZyBhcyBhdXRoZW50aWNhdGVkJyk7XG4gICAgICAgIHNldElzQXV0aGVudGljYXRlZCh0cnVlKTtcbiAgICAgIH1cbiAgICAgIC8vIFN0b3JlIHJvbGUgZnJvbSBzZXNzaW9uIGlmIG5vdCBzZXQgeWV0XG4gICAgICBjb25zdCBzZXNzUm9sZSA9IChzZXNzaW9uLnVzZXIgYXMgU2Vzc2lvblVzZXIpPy5yb2xlO1xuICAgICAgaWYgKHNlc3NSb2xlICYmIHVzZXJSb2xlICE9PSBzZXNzUm9sZSkge1xuICAgICAgICBjb25zb2xlLmxvZygnQXV0aFByb3ZpZGVyOiBTeW5jaW5nIHVzZXJSb2xlIGZyb20gTmV4dEF1dGggc2Vzc2lvbjonLCBzZXNzUm9sZSk7XG4gICAgICAgIHNldFVzZXJSb2xlKHNlc3NSb2xlKTtcbiAgICAgIH1cbiAgICAgIGlmIChsb2FkaW5nKSB7XG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoc3RhdHVzID09PSAndW5hdXRoZW50aWNhdGVkJykge1xuICAgICAgaWYgKGlzQXV0aGVudGljYXRlZCkge1xuICAgICAgICBjb25zb2xlLmxvZygnQXV0aFByb3ZpZGVyOiBTZXNzaW9uIGxvc3Qg4oCTIG1hcmtpbmcgYXMgdW5hdXRoZW50aWNhdGVkJyk7XG4gICAgICB9XG4gICAgICBzZXRJc0F1dGhlbnRpY2F0ZWQoZmFsc2UpO1xuICAgICAgaWYgKGxvYWRpbmcpIHtcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbc3RhdHVzLCBzZXNzaW9uLCBpc0F1dGhlbnRpY2F0ZWQsIHVzZXJSb2xlLCBsb2FkaW5nXSk7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuXG4gIC8vIFJlZiB0byByZW1lbWJlciB0aGUgbGFzdCBOZXh0QXV0aCBzdGF0dXMgd2Ugc2F3XG4gIGNvbnN0IHByZXZBdXRoU3RhdHVzUmVmID0gdXNlUmVmPHN0cmluZyB8IG51bGw+KG51bGwpO1xuXG4gIC8vIENvbXB1dGVkIHByb3BlcnRpZXMgZm9yIHJvbGUgY2hlY2tpbmdcbiAgY29uc3QgaXNQYXJlbnQgPSB1c2VyUm9sZSA9PT0gJ3BhcmVudCcgfHwgdXNlclJvbGUgPT09ICdib3RoJztcbiAgY29uc3QgaXNPcGVyYXRvciA9IHVzZXJSb2xlID09PSAnb3BlcmF0b3InIHx8IHVzZXJSb2xlID09PSAnYm90aCc7XG4gIFxuICAvLyBEZWJ1ZyBsb2dnaW5nIGFmdGVyIGFsbCBob29rcyBhcmUgZGVjbGFyZWRcbiAgY29uc29sZS5sb2coJ0F1dGhQcm92aWRlcjogSW5pdGlhbCBzdGF0ZScsIHsgbG9hZGluZywgaXNBdXRoZW50aWNhdGVkLCB1c2VyUm9sZSwgc2Vzc2lvbiB9KTtcblxuICAvLyBDYWxjdWxhdGUgZWZmZWN0aXZlIHJvbGUgYmFzZWQgb24gYWN0dWFsIHJvbGUgYW5kIGN1cnJlbnQgdmlldyBjb250ZXh0XG4gIGNvbnN0IGdldEVmZmVjdGl2ZVJvbGUgPSAoKSA9PiB7XG4gICAgLy8gR2V0IHJvbGUgZnJvbSBtdWx0aXBsZSBzb3VyY2VzLCBwcmlvcml0aXppbmcgc2Vzc2lvbiBkYXRhXG4gICAgY29uc3QgYWN0dWFsUm9sZSA9IChzZXNzaW9uPy51c2VyIGFzIFNlc3Npb25Vc2VyKT8ucm9sZSB8fCB1c2VyRGF0YT8ucm9sZSB8fCB1c2VyUm9sZSB8fCBudWxsO1xuICAgIFxuICAgIGNvbnNvbGUubG9nKCdBdXRoUHJvdmlkZXI6IGdldEVmZmVjdGl2ZVJvbGUgLSBTb3VyY2VzOicsIHtcbiAgICAgIHNlc3Npb25Sb2xlOiAoc2Vzc2lvbj8udXNlciBhcyBTZXNzaW9uVXNlcik/LnJvbGUsXG4gICAgICB1c2VyRGF0YVJvbGU6IHVzZXJEYXRhPy5yb2xlLFxuICAgICAgdXNlclJvbGVTdGF0ZTogdXNlclJvbGUsXG4gICAgICBhY3R1YWxSb2xlLFxuICAgICAgY3VycmVudFZpZXdSb2xlLFxuICAgICAgY3VycmVudFBhdGg6IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gd2luZG93LmxvY2F0aW9uLnBhdGhuYW1lIDogJydcbiAgICB9KTtcbiAgICBcbiAgICBpZiAoYWN0dWFsUm9sZSA9PT0gJ2JvdGgnKSB7XG4gICAgICAvLyBGb3IgZHVhbC1yb2xlIHVzZXJzLCB1c2UgdGhlIGN1cnJlbnQgdmlldyByb2xlIG9yIGludGVsbGlnZW50bHkgZGV0ZXJtaW5lIGZyb20gcGF0aFxuICAgICAgY29uc3QgY3VycmVudFBhdGggPSB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZSA6ICcnO1xuICAgICAgXG4gICAgICAvLyBJZiBvbiBvcGVyYXRvciBwYXRoIGJ1dCBubyB2aWV3IHJvbGUgc2V0LCBhc3N1bWUgb3BlcmF0b3IgaW50ZW50XG4gICAgICBpZiAoY3VycmVudFBhdGguc3RhcnRzV2l0aCgnL29wZXJhdG9yJykgJiYgIWN1cnJlbnRWaWV3Um9sZSkge1xuICAgICAgICBjb25zb2xlLmxvZygnQXV0aFByb3ZpZGVyOiBPbiBvcGVyYXRvciBwYXRoIHdpdGggbm8gdmlldyByb2xlLCBhc3N1bWluZyBvcGVyYXRvciBpbnRlbnQnKTtcbiAgICAgICAgcmV0dXJuICdvcGVyYXRvcic7XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIElmIG9uIHBhcmVudC9kYXNoYm9hcmQgcGF0aCBidXQgbm8gdmlldyByb2xlIHNldCwgYXNzdW1lIHBhcmVudCBpbnRlbnQgIFxuICAgICAgaWYgKChjdXJyZW50UGF0aCA9PT0gJy9kYXNoYm9hcmQnIHx8IGN1cnJlbnRQYXRoLnN0YXJ0c1dpdGgoJy9zdHVkZW50LWRhc2hib2FyZC8nKSkgJiYgIWN1cnJlbnRWaWV3Um9sZSkge1xuICAgICAgICBjb25zb2xlLmxvZygnQXV0aFByb3ZpZGVyOiBPbiBwYXJlbnQgcGF0aCB3aXRoIG5vIHZpZXcgcm9sZSwgYXNzdW1pbmcgcGFyZW50IGludGVudCcpO1xuICAgICAgICByZXR1cm4gJ3BhcmVudCc7XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIFVzZSB0aGUgY3VycmVudCB2aWV3IHJvbGUgb3IgZGVmYXVsdCB0byBwYXJlbnRcbiAgICAgIHJldHVybiBjdXJyZW50Vmlld1JvbGUgfHwgJ3BhcmVudCc7XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBhY3R1YWxSb2xlO1xuICB9O1xuXG4gIGNvbnN0IG1hbnVhbFN5bmNXaXRoTG9jYWxTdG9yYWdlID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGNvbnN0IHN0b3JlZFVzZXJEYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3VzZXJfZGF0YScpO1xuICAgIGlmIChzdG9yZWRVc2VyRGF0YSkge1xuICAgICAgc2V0VXNlckRhdGEoSlNPTi5wYXJzZShzdG9yZWRVc2VyRGF0YSkpO1xuICAgIH1cbiAgICBjb25zdCBzdG9yZWRDaGlsZHJlbkRhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY2hpbGRyZW5fZGF0YScpO1xuICAgIGlmIChzdG9yZWRDaGlsZHJlbkRhdGEpIHtcbiAgICAgIHNldENoaWxkcmVuRGF0YShKU09OLnBhcnNlKHN0b3JlZENoaWxkcmVuRGF0YSkpO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIGNvbnN0IHJlZnJlc2hVc2VyRGF0YSA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXVzZXI/LnVpZCkge1xuICAgICAgY29uc29sZS5sb2coXCJBdXRoUHJvdmlkZXI6IHJlZnJlc2hVc2VyRGF0YSAtIE5vIHVzZXIudWlkLCBleGl0aW5nLlwiKTtcbiAgICAgIHNldFVzZXJEYXRhKG51bGwpO1xuICAgICAgc2V0VXNlclJvbGUobnVsbCk7XG4gICAgICBzZXRDdXJyZW50Vmlld1JvbGUobnVsbCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIGNvbnNvbGUubG9nKGBBdXRoUHJvdmlkZXI6IFJlZnJlc2hpbmcgdXNlciBkYXRhIGZvciBVSUQ6ICR7dXNlci51aWR9YCk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gRmlyc3QsIGNoZWNrIGN1c3RvbSBjbGFpbXMgZm9yIHJvbGUgaW5mb3JtYXRpb25cbiAgICAgIGNvbnN0IHRva2VuID0gYXdhaXQgdXNlci5nZXRJZFRva2VuKHRydWUpOyAvLyBGb3JjZSByZWZyZXNoXG4gICAgICBjb25zdCBkZWNvZGVkVG9rZW4gPSBhd2FpdCB1c2VyLmdldElkVG9rZW5SZXN1bHQoKTtcbiAgICAgIFxuICAgICAgY29uc3QgY3VzdG9tUm9sZSA9IGRlY29kZWRUb2tlbi5jbGFpbXMucm9sZSBhcyBzdHJpbmc7XG4gICAgICBjb25zdCBpc1BhcmVudFJvbGUgPSBkZWNvZGVkVG9rZW4uY2xhaW1zLmlzUGFyZW50IGFzIGJvb2xlYW47XG4gICAgICBjb25zdCBpc09wZXJhdG9yUm9sZSA9IGRlY29kZWRUb2tlbi5jbGFpbXMuaXNPcGVyYXRvciBhcyBib29sZWFuO1xuICAgICAgXG4gICAgICBjb25zb2xlLmxvZygnQXV0aFByb3ZpZGVyOiBUb2tlbiBjbGFpbXM6JywgeyBjdXN0b21Sb2xlLCBpc1BhcmVudFJvbGUsIGlzT3BlcmF0b3JSb2xlIH0pO1xuICAgICAgXG4gICAgICAvLyBEZXRlcm1pbmUgY29tcG9zaXRlIHJvbGVcbiAgICAgIGxldCBmaW5hbFJvbGUgPSBjdXN0b21Sb2xlO1xuICAgICAgaWYgKGlzUGFyZW50Um9sZSAmJiBpc09wZXJhdG9yUm9sZSkge1xuICAgICAgICBmaW5hbFJvbGUgPSAnYm90aCc7XG4gICAgICAgIGNvbnNvbGUubG9nKCdBdXRoUHJvdmlkZXI6IFVzZXIgaGFzIGJvdGggcGFyZW50IGFuZCBvcGVyYXRvciByb2xlcycpO1xuICAgICAgfSBlbHNlIGlmIChpc1BhcmVudFJvbGUpIHtcbiAgICAgICAgZmluYWxSb2xlID0gJ3BhcmVudCc7XG4gICAgICB9IGVsc2UgaWYgKGlzT3BlcmF0b3JSb2xlKSB7XG4gICAgICAgIGZpbmFsUm9sZSA9ICdvcGVyYXRvcic7XG4gICAgICB9XG4gICAgICBcbiAgICAgIHNldFVzZXJSb2xlKGZpbmFsUm9sZSk7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndXNlcl9yb2xlJywgZmluYWxSb2xlKTtcbiAgICAgIFxuICAgICAgLy8gU2V0IGRlZmF1bHQgdmlldyByb2xlIGlmIG5vdCBzZXRcbiAgICAgIGlmICghY3VycmVudFZpZXdSb2xlKSB7XG4gICAgICAgIGlmIChmaW5hbFJvbGUgPT09ICdib3RoJykge1xuICAgICAgICAgIC8vIERlZmF1bHQgdG8gcGFyZW50IHZpZXcgZm9yIHVzZXJzIHdpdGggYm90aCByb2xlc1xuICAgICAgICAgIHNldEN1cnJlbnRWaWV3Um9sZSgncGFyZW50Jyk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0F1dGhQcm92aWRlcjogU2V0dGluZyBkZWZhdWx0IHZpZXcgdG8gcGFyZW50IGZvciBkdWFsLXJvbGUgdXNlcicpO1xuICAgICAgICB9IGVsc2UgaWYgKGZpbmFsUm9sZSA9PT0gJ3BhcmVudCcgfHwgZmluYWxSb2xlID09PSAnb3BlcmF0b3InKSB7XG4gICAgICAgICAgc2V0Q3VycmVudFZpZXdSb2xlKGZpbmFsUm9sZSBhcyAncGFyZW50JyB8ICdvcGVyYXRvcicpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIC4uLmV4aXN0aW5nIHJvbGUtc3BlY2lmaWMgZGF0YSBmZXRjaGluZyBsb2dpYy4uLlxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignQXV0aFByb3ZpZGVyOiBFcnJvciBkdXJpbmcgcmVmcmVzaFVzZXJEYXRhOicsIGVycik7XG4gICAgICBzZXRFcnJvcignRmFpbGVkIHRvIHJlZnJlc2ggdXNlciBkYXRhLiBQbGVhc2UgdHJ5IGxvZ2dpbmcgaW4gYWdhaW4uJyk7XG4gICAgICBzZXRVc2VyRGF0YShudWxsKTtcbiAgICAgIHNldFVzZXJSb2xlKG51bGwpO1xuICAgICAgc2V0Q3VycmVudFZpZXdSb2xlKG51bGwpO1xuICAgIH1cbiAgfSwgW3VzZXJdKTtcblxuICBjb25zdCByZWZyZXNoQ2hpbGRyZW5EYXRhID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmICghdXNlcj8udWlkKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgLy8gRmV0Y2ggcGFyZW50IGRhc2hib2FyZCB3aGljaCBzaG91bGQgaW5jbHVkZSBjaGlsZHJlbiBkYXRhXG4gICAgICBjb25zdCBkYXNoYm9hcmRSZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3BhcmVudC9kYXNoYm9hcmQ/cGFyZW50SWQ9JHt1c2VyLnVpZH1gKTtcbiAgICAgIGNvbnN0IGRhc2hib2FyZERhdGEgPSBhd2FpdCBkYXNoYm9hcmRSZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChkYXNoYm9hcmRSZXNwb25zZS5vayAmJiBkYXNoYm9hcmREYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgY29uc3QgY2hpbGRyZW4gPSBkYXNoYm9hcmREYXRhLmNoaWxkcmVuIHx8IGRhc2hib2FyZERhdGEuZGF0YT8uY2hpbGRyZW4gfHwgW107XG4gICAgICAgIHNldENoaWxkcmVuRGF0YShjaGlsZHJlbik7XG4gICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdjaGlsZHJlbl9kYXRhJywgSlNPTi5zdHJpbmdpZnkoY2hpbGRyZW4pKTtcblxuICAgICAgICAvLyBLZWVwIGEgc2ltcGxlIGxpc3Qgb2YgY2hpbGQgaWRzIGluIHRoZSB1c2VyJ3MgZG9jIGZvciBxdWljayBsb29rLXVwc1xuICAgICAgICBpZiAoY2hpbGRyZW4ubGVuZ3RoID4gMCkge1xuICAgICAgICAgIGNvbnN0IHBhcmVudFJlZiA9IGRvYyhkYiwgJ3VzZXJzJywgdXNlci51aWQpO1xuICAgICAgICAgIGF3YWl0IHVwZGF0ZURvYyhwYXJlbnRSZWYsIHtcbiAgICAgICAgICAgIGNoaWxkcmVuOiBjaGlsZHJlbi5tYXAoKGNoaWxkOiBhbnkpID0+IGNoaWxkLmlkKSxcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm47IC8vIGRvbmVcbiAgICAgIH1cblxuICAgICAgLy8gRmFsbGJhY2s6IGRlZGljYXRlZCBjaGlsZHJlbiBlbmRwb2ludFxuICAgICAgY29uc3QgY2hpbGRyZW5SZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3BhcmVudC9nZXQtY2hpbGRyZW4/cGFyZW50SWQ9JHt1c2VyLnVpZH1gKTtcbiAgICAgIGNvbnN0IGNoaWxkcmVuUmVzRGF0YSA9IGF3YWl0IGNoaWxkcmVuUmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAoY2hpbGRyZW5SZXNwb25zZS5vayAmJiBjaGlsZHJlblJlc0RhdGEuc3VjY2Vzcykge1xuICAgICAgICBjb25zdCBjaGlsZHJlbiA9IGNoaWxkcmVuUmVzRGF0YS5jaGlsZHJlbiB8fCBbXTtcbiAgICAgICAgc2V0Q2hpbGRyZW5EYXRhKGNoaWxkcmVuKTtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2NoaWxkcmVuX2RhdGEnLCBKU09OLnN0cmluZ2lmeShjaGlsZHJlbikpO1xuXG4gICAgICAgIGlmIChjaGlsZHJlbi5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgY29uc3QgcGFyZW50UmVmID0gZG9jKGRiLCAndXNlcnMnLCB1c2VyLnVpZCk7XG4gICAgICAgICAgYXdhaXQgdXBkYXRlRG9jKHBhcmVudFJlZiwge1xuICAgICAgICAgICAgY2hpbGRyZW46IGNoaWxkcmVuLm1hcCgoY2hpbGQ6IGFueSkgPT4gY2hpbGQuaWQpLFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zdCBtc2cgPSBjaGlsZHJlblJlc0RhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byBmZXRjaCBjaGlsZHJlbiBkYXRhJztcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKG1zZyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdBdXRoUHJvdmlkZXI6IEVycm9yIGZldGNoaW5nIGNoaWxkcmVuIGRhdGE6JywgZXJyKTtcbiAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIGZldGNoIGNoaWxkcmVuIGRhdGEnKTtcbiAgICB9XG4gIH0sIFt1c2VyXSk7XG5cbiAgY29uc3QgaGFuZGxlTG9naW5TdWNjZXNzID0gdXNlQ2FsbGJhY2soYXN5bmMgKGN1c3RvbVRva2VuOiBzdHJpbmcsIHN0dWRlbnRJZEhpbnQ/OiBzdHJpbmcpID0+IHtcbiAgICAvLyAqKiogUkVNT1ZFIEFMRVJUICoqKlxuICAgIC8vIGFsZXJ0KFwiQXV0aFByb3ZpZGVyOiBoYW5kbGVMb2dpblN1Y2Nlc3MgQ0FMTEVEIVwiKTsgXG4gICAgLy8gKioqIEVORCBSRU1PVkUgQUxFUlQgKioqXG5cbiAgICBjb25zb2xlLmxvZyhcIkF1dGhQcm92aWRlcjogaGFuZGxlTG9naW5TdWNjZXNzIGNhbGxlZC5cIik7IC8vIExvZyBzdGFydFxuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7ICAgIHRyeSB7XG4gICAgICAvLyBWYWxpZGF0ZSB0b2tlbiBmb3JtYXRcbiAgICAgIGlmICghY3VzdG9tVG9rZW4gfHwgdHlwZW9mIGN1c3RvbVRva2VuICE9PSAnc3RyaW5nJykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgdG9rZW4gZm9ybWF0OiBUb2tlbiBpcyBtaXNzaW5nIG9yIG5vdCBhIHN0cmluZy4nKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgaWYgKCFjdXN0b21Ub2tlbi5pbmNsdWRlcygnLicpKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCB0b2tlbiBmb3JtYXQ6IFRva2VuIGRvZXMgbm90IGFwcGVhciB0byBiZSBhIHZhbGlkIEpXVC4nKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgaWYgKGN1c3RvbVRva2VuLmxlbmd0aCA8IDUwKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCB0b2tlbiBmb3JtYXQ6IFRva2VuIGlzIHRvbyBzaG9ydCB0byBiZSB2YWxpZC4nKTtcbiAgICAgIH0gICAgICAvLyBTaWduIGluIHVzaW5nIHRoZSBjdXN0b20gdG9rZW4gd2l0aCB0aW1lb3V0XG4gICAgICBjb25zb2xlLmxvZyhcIkF1dGhQcm92aWRlcjogQ2FsbGluZyBzaWduSW5XaXRoQ3VzdG9tVG9rZW4uLi5cIik7IC8vIExvZyBiZWZvcmUgY2FsbFxuICAgICAgXG4gICAgICBjb25zdCBzaWduSW5Qcm9taXNlID0gc2lnbkluV2l0aEN1c3RvbVRva2VuKGF1dGgsIGN1c3RvbVRva2VuKTtcbiAgICAgIGNvbnN0IHRpbWVvdXRQcm9taXNlID0gbmV3IFByb21pc2UoKF8sIHJlamVjdCkgPT4gXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4gcmVqZWN0KG5ldyBFcnJvcignU2lnbi1pbiB0aW1lb3V0OiBUaGUgb3BlcmF0aW9uIHRvb2sgdG9vIGxvbmcgdG8gY29tcGxldGUnKSksIDMwMDAwKVxuICAgICAgKTtcbiAgICAgIFxuICAgICAgY29uc3QgdXNlckNyZWRlbnRpYWwgPSBhd2FpdCBQcm9taXNlLnJhY2UoW3NpZ25JblByb21pc2UsIHRpbWVvdXRQcm9taXNlXSkgYXMgYW55O1xuICAgICAgY29uc3QgbG9nZ2VkSW5Vc2VyID0gdXNlckNyZWRlbnRpYWwudXNlcjtcbiAgICAgIGNvbnNvbGUubG9nKFwiQXV0aFByb3ZpZGVyOiBzaWduSW5XaXRoQ3VzdG9tVG9rZW4gc3VjY2Vzc2Z1bC4gVXNlcjpcIiwgbG9nZ2VkSW5Vc2VyLnVpZCk7XG5cbiAgICAgIC8vICoqKiBBREQgTE9HR0lORzogQ2hlY2sgYXV0aC5jdXJyZW50VXNlciBpbW1lZGlhdGVseSBhZnRlciBzaWduLWluICoqKlxuICAgICAgY29uc29sZS5sb2coYEF1dGhQcm92aWRlcjogYXV0aC5jdXJyZW50VXNlcj8udWlkIGltbWVkaWF0ZWx5IGFmdGVyIHNpZ25JbldpdGhDdXN0b21Ub2tlbjogJHthdXRoLmN1cnJlbnRVc2VyPy51aWR9YCk7XG5cbiAgICAgIC8vIEZvcmNlIHRva2VuIHJlZnJlc2ggdG8gZ2V0IGN1c3RvbSBjbGFpbXNcbiAgICAgIGNvbnNvbGUubG9nKFwiQXV0aFByb3ZpZGVyOiBGb3JjaW5nIHRva2VuIHJlZnJlc2ggKGdldElkVG9rZW4odHJ1ZSkpLi4uXCIpOyAvLyBMb2cgYmVmb3JlIHJlZnJlc2hcbiAgICAgIGF3YWl0IGxvZ2dlZEluVXNlci5nZXRJZFRva2VuKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coXCJBdXRoUHJvdmlkZXI6IFRva2VuIHJlZnJlc2ggY29tcGxldGUuXCIpOyAvLyBMb2cgYWZ0ZXIgcmVmcmVzaFxuXG4gICAgICAvLyAqKiogQUREIExPR0dJTkc6IENoZWNrIGF1dGguY3VycmVudFVzZXIgYWZ0ZXIgdG9rZW4gcmVmcmVzaCAqKipcbiAgICAgIGNvbnNvbGUubG9nKGBBdXRoUHJvdmlkZXI6IGF1dGguY3VycmVudFVzZXI/LnVpZCBhZnRlciB0b2tlbiByZWZyZXNoOiAke2F1dGguY3VycmVudFVzZXI/LnVpZH1gKTtcblxuICAgICAgLy8gVXNlIHByb3ZpZGVkIHN0dWRlbnRJZCBvciBsb29rIGluIGxvY2FsU3RvcmFnZSAtIEJVVCBETyBOT1QgU0VUIEFTIFNFU1NJT04gSURcbiAgICAgIGNvbnN0IHN0dWRlbnRJZCA9IHN0dWRlbnRJZEhpbnQgfHwgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3N0dWRlbnRfaWQnKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgIGlmIChzdHVkZW50SWQpIHtcbiAgICAgICAgLy8gT25seSBzdG9yZSB0aGUgc3R1ZGVudCBJRCwgbm90IGFzIGEgc2Vzc2lvbiBJRFxuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnc3R1ZGVudF9pZCcsIHN0dWRlbnRJZCk7XG4gICAgICAgIFxuICAgICAgICAvLyBSZW1vdmUgYW55IGV4aXN0aW5nIGluY29ycmVjdCBzZXNzaW9uIElEIHRoYXQgbWlnaHQgYmUgdGhlIHN0dWRlbnQgSURcbiAgICAgICAgaWYgKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdjdXJyZW50X3Nlc3Npb24nKSA9PT0gc3R1ZGVudElkKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coXCJBdXRoUHJvdmlkZXI6IFJlbW92aW5nIGluY29ycmVjdCBzZXNzaW9uIElEICh3YXMgc2V0IHRvIHN0dWRlbnQgSUQpXCIpO1xuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdjdXJyZW50X3Nlc3Npb24nKTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLy8gU2V0IGNsYWltcyB2aWEgQVBJIGltbWVkaWF0ZWx5IGFmdGVyIGxvZ2luXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgaWRUb2tlbiA9IGF3YWl0IGxvZ2dlZEluVXNlci5nZXRJZFRva2VuKCk7XG4gICAgICAgICAgY29uc3QgY2xhaW1SZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvc2V0LXN0dWRlbnQtY2xhaW1zJywge1xuICAgICAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke2lkVG9rZW59YFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgc3R1ZGVudElkOiBzdHVkZW50SWQgfSlcbiAgICAgICAgICB9KTtcbiAgICAgICAgICBcbiAgICAgICAgICBpZiAoY2xhaW1SZXNwb25zZS5vaykge1xuICAgICAgICAgICAgY29uc29sZS5sb2coXCJBdXRoUHJvdmlkZXI6IFN0dWRlbnQgY2xhaW1zIHNldCBzdWNjZXNzZnVsbHlcIik7XG4gICAgICAgICAgICAvLyBGb3JjZSBhbm90aGVyIHRva2VuIHJlZnJlc2ggdG8gZ2V0IHRoZSBuZXcgY2xhaW1zXG4gICAgICAgICAgICBhd2FpdCBsb2dnZWRJblVzZXIuZ2V0SWRUb2tlbih0cnVlKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgbGV0IGVycm9yTWVzc2FnZSA9IGBTdGF0dXM6ICR7Y2xhaW1SZXNwb25zZS5zdGF0dXN9YDtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IGNsYWltUmVzcG9uc2UuanNvbigpO1xuICAgICAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvckRhdGEuZXJyb3IgfHwgZXJyb3JNZXNzYWdlO1xuICAgICAgICAgICAgfSBjYXRjaCAoanNvbkVycm9yKSB7XG4gICAgICAgICAgICAgIC8vIElmIHJlc3BvbnNlIGlzIG5vdCBKU09OLCB0cnkgdG8gZ2V0IHRleHRcbiAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBlcnJvclRleHQgPSBhd2FpdCBjbGFpbVJlc3BvbnNlLnRleHQoKTtcbiAgICAgICAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvclRleHQgfHwgZXJyb3JNZXNzYWdlO1xuICAgICAgICAgICAgICB9IGNhdGNoICh0ZXh0RXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiQXV0aFByb3ZpZGVyOiBDb3VsZCBub3QgcGFyc2UgZXJyb3IgcmVzcG9uc2U6XCIsIHRleHRFcnJvcik7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJBdXRoUHJvdmlkZXI6IEZhaWxlZCB0byBzZXQgc3R1ZGVudCBjbGFpbXM6XCIsIGVycm9yTWVzc2FnZSk7XG4gICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcihcIkF1dGhQcm92aWRlcjogRXJyb3Igc2V0dGluZyBzdHVkZW50IGNsYWltczpcIiwgZSk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gQ2xlYXIgYW55IHBhcmVudC1yZWxhdGVkIGZsYWdzIGZpcnN0XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgncGFyZW50X2lkJyk7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgncGFyZW50X25hbWUnKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdwYXJlbnRfcm9sZScpO1xuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3ZpZXdpbmdfYXNfY2hpbGQnKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdpc19wYXJlbnQnKTtcblxuICAgICAgLy8gU2V0IHVzZXIgcm9sZSBhbmQgYXV0aCBzdGF0ZVxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3VzZXJfcm9sZScsICdzdHVkZW50Jyk7XG4gICAgICBzZXRVc2VyUm9sZSgnc3R1ZGVudCcpO1xuICAgICAgc2V0SXNBdXRoZW50aWNhdGVkKHRydWUpO1xuICAgICAgXG4gICAgICAvLyBDbGVhciB0aGUgcHJvZ3Jlc3MgZmxhZyBhZnRlciBzdWNjZXNzZnVsIHNpZ24taW5cbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdsb2dpbl9pbl9wcm9ncmVzcycpO1xuICAgICAgY29uc29sZS5sb2coXCJBdXRoUHJvdmlkZXI6IExvZ2luIHN1Y2Nlc3NmdWwsIGNsZWFyZWQgbG9naW5faW5fcHJvZ3Jlc3MgZmxhZy5cIik7ICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkF1dGhQcm92aWRlcjogRXJyb3Igc2lnbmluZyBpbiB3aXRoIGN1c3RvbSB0b2tlbjpcIiwgZXJyKTtcbiAgICAgIFxuICAgICAgbGV0IGVycm9yTWVzc2FnZSA9IFwiRmFpbGVkIHRvIHNpZ24gaW4gd2l0aCBjdXN0b20gdG9rZW4uXCI7XG4gICAgICBpZiAoZXJyIGluc3RhbmNlb2YgRXJyb3IpIHtcbiAgICAgICAgaWYgKGVyci5tZXNzYWdlLmluY2x1ZGVzKCdhdXRoL3F1b3RhLWV4Y2VlZGVkJykpIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBcIkF1dGhlbnRpY2F0aW9uIHF1b3RhIGV4Y2VlZGVkLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLlwiO1xuICAgICAgICB9IGVsc2UgaWYgKGVyci5tZXNzYWdlLmluY2x1ZGVzKCdhdXRoL2ludmFsaWQtY3VzdG9tLXRva2VuJykpIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBcIkludmFsaWQgYXV0aGVudGljYXRpb24gdG9rZW4uIFBsZWFzZSB0cnkgbG9nZ2luZyBpbiBhZ2Fpbi5cIjtcbiAgICAgICAgfSBlbHNlIGlmIChlcnIubWVzc2FnZS5pbmNsdWRlcygnYXV0aC9jdXN0b20tdG9rZW4tbWlzbWF0Y2gnKSkge1xuICAgICAgICAgIGVycm9yTWVzc2FnZSA9IFwiQXV0aGVudGljYXRpb24gdG9rZW4gbWlzbWF0Y2guIFBsZWFzZSB0cnkgbG9nZ2luZyBpbiBhZ2Fpbi5cIjtcbiAgICAgICAgfSBlbHNlIGlmIChlcnIubWVzc2FnZS5pbmNsdWRlcygnYXV0aC9uZXR3b3JrLXJlcXVlc3QtZmFpbGVkJykpIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBcIk5ldHdvcmsgZXJyb3IuIFBsZWFzZSBjaGVjayB5b3VyIGludGVybmV0IGNvbm5lY3Rpb24gYW5kIHRyeSBhZ2Fpbi5cIjtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnIubWVzc2FnZTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgXG4gICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgICAgc2V0VXNlcihudWxsKTtcbiAgICAgIHNldFVzZXJEYXRhKG51bGwpO1xuICAgICAgc2V0Q2hpbGRyZW5EYXRhKG51bGwpO1xuICAgICAgc2V0VXNlclJvbGUobnVsbCk7XG4gICAgICBzZXRTdHVkZW50U2Vzc2lvbihudWxsKTtcbiAgICAgIC8vIENsZWFyIGZsYWdzL3N0b3JhZ2Ugb24gZXJyb3JcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdhdXRoX3Rva2VuJyk7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnc3R1ZGVudF9pZCcpO1xuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3VzZXJfcm9sZScpO1xuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2N1cnJlbnRfc2Vzc2lvbicpO1xuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2xvZ2luX2luX3Byb2dyZXNzJyk7XG4gICAgICBcbiAgICAgIC8vIFJlLXRocm93IHRoZSBlcnJvciBzbyBpdCBjYW4gYmUgY2F1Z2h0IGJ5IHRoZSBjYWxsZXJcbiAgICAgIHRocm93IGVycjtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgLy8gc2V0TG9hZGluZyhmYWxzZSk7IC8vIExvYWRpbmcgc2hvdWxkIGJlIHNldCB0byBmYWxzZSBieSB0aGUgb25BdXRoU3RhdGVDaGFuZ2VkIGxpc3RlbmVyIGhhbmRsaW5nXG4gICAgICBjb25zb2xlLmxvZyhcIkF1dGhQcm92aWRlcjogaGFuZGxlTG9naW5TdWNjZXNzIGZpbmFsbHkgYmxvY2suXCIpOyAvLyBMb2cgZmluYWxseVxuICAgIH1cbiAgfSwgW10pOyAvLyBSZW1vdmVkIGF1dGggZGVwZW5kZW5jeSBhcyBpdCdzIGdsb2JhbGx5IGF2YWlsYWJsZVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc29sZS5sb2coXCJBdXRoUHJvdmlkZXI6IFNldHRpbmcgdXAgb25BdXRoU3RhdGVDaGFuZ2VkIGxpc3RlbmVyLlwiKTsgLy8gTG9nIGxpc3RlbmVyIHNldHVwXG4gICAgY29uc3QgdW5zdWJzY3JpYmUgPSBvbkF1dGhTdGF0ZUNoYW5nZWQoYXV0aCwgYXN5bmMgKHVzZXIpID0+IHtcbiAgICAgIC8vICoqKiBBREQgREVUQUlMRUQgTE9HR0lORyBmb3Igb25BdXRoU3RhdGVDaGFuZ2VkICoqKlxuICAgICAgY29uc3QgdGltZXN0YW1wID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpO1xuICAgICAgY29uc29sZS5sb2coYEF1dGhQcm92aWRlcjogb25BdXRoU3RhdGVDaGFuZ2VkIGZpcmVkIGF0ICR7dGltZXN0YW1wfS4gVXNlciBvYmplY3Q6YCwgdXNlciA/IHsgdWlkOiB1c2VyLnVpZCwgZW1haWw6IHVzZXIuZW1haWwgfSA6IG51bGwpO1xuXG4gICAgICBzZXRVc2VyKHVzZXIpOyAvLyBVcGRhdGUgdGhlIHVzZXIgc3RhdGVcblxuICAgICAgaWYgKHVzZXIpIHtcbiAgICAgICAgY29uc29sZS5sb2coYEF1dGhQcm92aWRlcjogb25BdXRoU3RhdGVDaGFuZ2VkIC0gVXNlciBpcyBwcmVzZW50IChVSUQ6ICR7dXNlci51aWR9KS5gKTtcbiAgICAgICAgLy8gVHJ5IHRvIHJlZnJlc2ggdG9rZW4gdG8gZ2V0IGxhdGVzdCBjbGFpbXNcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIkF1dGhQcm92aWRlcjogb25BdXRoU3RhdGVDaGFuZ2VkIC0gUmVmcmVzaGluZyB0b2tlbi4uLlwiKTtcbiAgICAgICAgICBhd2FpdCB1c2VyLmdldElkVG9rZW4odHJ1ZSk7XG4gICAgICAgICAgY29uc29sZS5sb2coXCJBdXRoUHJvdmlkZXI6IG9uQXV0aFN0YXRlQ2hhbmdlZCAtIFRva2VuIHJlZnJlc2hlZC5cIik7XG5cbiAgICAgICAgICAvLyAuLi4gY2hlY2sgY2xhaW1zIC4uLlxuICAgICAgICAgIGNvbnN0IGlkVG9rZW5SZXN1bHQgPSBhd2FpdCB1c2VyLmdldElkVG9rZW5SZXN1bHQoKTtcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIkF1dGhQcm92aWRlcjogb25BdXRoU3RhdGVDaGFuZ2VkIC0gVG9rZW4gY2xhaW1zOlwiLCBpZFRva2VuUmVzdWx0LmNsYWltcyk7XG4gICAgICAgICAgLy8gLi4uIHNldCByb2xlL3Nlc3Npb24gZnJvbSBjbGFpbXMgLi4uXG5cbiAgICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJBdXRoUHJvdmlkZXI6IG9uQXV0aFN0YXRlQ2hhbmdlZCAtIEVycm9yIHJlZnJlc2hpbmcgdG9rZW46XCIsIGUpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gVHJ5IHRvIGxvYWQgZnJvbSBsb2NhbFN0b3JhZ2UgZmlyc3QgZm9yIGZhc3RlciBpbml0aWFsIHJlbmRlclxuICAgICAgICBjb25zdCBzdG9yZWRVc2VyRGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VyX2RhdGEnKTtcbiAgICAgICAgaWYgKHN0b3JlZFVzZXJEYXRhKSB7XG4gICAgICAgICAgc2V0VXNlckRhdGEoSlNPTi5wYXJzZShzdG9yZWRVc2VyRGF0YSkpO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBjb25zdCBzdG9yZWRDaGlsZHJlbkRhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY2hpbGRyZW5fZGF0YScpO1xuICAgICAgICBpZiAoc3RvcmVkQ2hpbGRyZW5EYXRhKSB7XG4gICAgICAgICAgc2V0Q2hpbGRyZW5EYXRhKEpTT04ucGFyc2Uoc3RvcmVkQ2hpbGRyZW5EYXRhKSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBUaGVuIHJlZnJlc2ggZnJvbSBzZXJ2ZXJcbiAgICAgICAgY29uc29sZS5sb2coXCJBdXRoUHJvdmlkZXI6IG9uQXV0aFN0YXRlQ2hhbmdlZCAtIENhbGxpbmcgcmVmcmVzaFVzZXJEYXRhLi4uXCIpO1xuICAgICAgICBhd2FpdCByZWZyZXNoVXNlckRhdGEoKTtcbiAgICAgICAgY29uc29sZS5sb2coXCJBdXRoUHJvdmlkZXI6IG9uQXV0aFN0YXRlQ2hhbmdlZCAtIHJlZnJlc2hVc2VyRGF0YSBjb21wbGV0ZS5cIik7XG4gICAgICAgIHNldElzQXV0aGVudGljYXRlZCh0cnVlKTtcbiAgICAgICAgY29uc29sZS5sb2coXCJBdXRoUHJvdmlkZXI6IG9uQXV0aFN0YXRlQ2hhbmdlZCAtIFNldCBpc0F1dGhlbnRpY2F0ZWQgPSB0cnVlLlwiKTtcblxuICAgICAgICAvLyBPbmx5IHJlZnJlc2ggY2hpbGRyZW4gZGF0YSBpZiB1c2VyIGlzIGEgcGFyZW50XG4gICAgICAgIGlmICh1c2VyRGF0YT8ucm9sZSA9PT0gJ3BhcmVudCcgfHwgSlNPTi5wYXJzZShzdG9yZWRVc2VyRGF0YSB8fCAne30nKT8ucm9sZSA9PT0gJ3BhcmVudCcpIHtcbiAgICAgICAgICBhd2FpdCByZWZyZXNoQ2hpbGRyZW5EYXRhKCk7XG4gICAgICAgIH1cblxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5sb2coXCJBdXRoUHJvdmlkZXI6IG9uQXV0aFN0YXRlQ2hhbmdlZCAtIFVzZXIgaXMgbnVsbC5cIik7XG4gICAgICAgIC8vIC4uLiBjbGVhciB1c2VyIHN0YXRlIGFuZCBsb2NhbFN0b3JhZ2UgLi4uXG4gICAgICAgIHNldFVzZXJEYXRhKG51bGwpO1xuICAgICAgICBzZXRDaGlsZHJlbkRhdGEobnVsbCk7XG4gICAgICAgIHNldFVzZXJSb2xlKG51bGwpO1xuICAgICAgICBzZXRTdHVkZW50U2Vzc2lvbihudWxsKTtcbiAgICAgICAgc2V0SXNBdXRoZW50aWNhdGVkKGZhbHNlKTtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3VzZXJfZGF0YScpO1xuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnY2hpbGRyZW5fZGF0YScpO1xuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnQ1VSUkVOVF9TRVNTSU9OX0tFWScpOyAvLyBFbnN1cmUgY29ycmVjdCBrZXkgaWYgdXNlZCBlbHNld2hlcmVcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3VzZXJfcm9sZScpO1xuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnc3R1ZGVudF9pZCcpO1xuICAgICAgICBjb25zb2xlLmxvZyhcIkF1dGhQcm92aWRlcjogb25BdXRoU3RhdGVDaGFuZ2VkIC0gQ2xlYXJlZCBzdGF0ZSBhbmQgbG9jYWxTdG9yYWdlLlwiKTtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coYEF1dGhQcm92aWRlcjogb25BdXRoU3RhdGVDaGFuZ2VkIC0gU2V0dGluZyBsb2FkaW5nID0gZmFsc2UgYXQgJHtuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCl9LmApO1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfSk7XG5cbiAgICAvLyBDbGVhbnVwIGZ1bmN0aW9uXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKFwiQXV0aFByb3ZpZGVyOiBDbGVhbmluZyB1cCBvbkF1dGhTdGF0ZUNoYW5nZWQgbGlzdGVuZXIuXCIpOyAvLyBMb2cgY2xlYW51cFxuICAgICAgdW5zdWJzY3JpYmUoKTtcbiAgICB9O1xuICB9LCBbcmVmcmVzaFVzZXJEYXRhLCByZWZyZXNoQ2hpbGRyZW5EYXRhXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBTa2lwIGlmIHN0aWxsIGxvYWRpbmcgYXV0aCBvciBzZXNzaW9uXG4gICAgaWYgKGxvYWRpbmcgfHwgc3RhdHVzID09PSAnbG9hZGluZycpIHJldHVybjtcbiAgICBcbiAgICAvLyBIYW5kbGUgdXNlciBhdXRoZW50aWNhdGlvbiBzdGF0ZSBmb3IgYm90aCBGaXJlYmFzZSBhbmQgTmV4dEF1dGhcbiAgICBjb25zdCBzZXNzaW9uVXNlciA9IHNlc3Npb24/LnVzZXIgYXMgU2Vzc2lvblVzZXIgfCB1bmRlZmluZWQ7XG4gICAgY29uc3QgaXNBdXRoZW50aWNhdGVkID0gKCEhdXNlciAmJiAhIXVzZXIudWlkKSB8fCBzdGF0dXMgPT09ICdhdXRoZW50aWNhdGVkJztcbiAgICBcbiAgICBpZiAoaXNBdXRoZW50aWNhdGVkKSB7XG4gICAgICAvLyBJZiB1c2VyIGlzIGF1dGhlbnRpY2F0ZWQgYnV0IG5vIHJvbGUgaXMgc2V0LCBhdHRlbXB0IHRvIGRldGVybWluZSByb2xlXG4gICAgICBpZiAoIXVzZXJSb2xlKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKFwiQXV0aFByb3ZpZGVyOiBVc2VyIGF1dGhlbnRpY2F0ZWQgYnV0IG5vIHJvbGUgc2V0LiBBdHRlbXB0aW5nIHRvIGRldGVybWluZSByb2xlLi4uXCIpO1xuICAgICAgICBcbiAgICAgICAgLy8gRmlyc3QgY2hlY2sgTmV4dEF1dGggc2Vzc2lvbiBmb3Igcm9sZSAoZm9yIHBhcmVudHMvb3BlcmF0b3JzKVxuICAgICAgICBpZiAoc3RhdHVzID09PSAnYXV0aGVudGljYXRlZCcgJiYgc2Vzc2lvblVzZXI/LnJvbGUpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhgQXV0aFByb3ZpZGVyOiBGb3VuZCByb2xlIGluIE5leHRBdXRoIHNlc3Npb246ICR7c2Vzc2lvblVzZXIucm9sZX1gKTtcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBGb3IgTmV4dEF1dGggdXNlcnMsIGdldCBjb21wcmVoZW5zaXZlIHJvbGUgaW5mb3JtYXRpb24gZnJvbSBBUElcbiAgICAgICAgICBjb25zdCBmZXRjaFVzZXJSb2xlID0gYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ0F1dGhQcm92aWRlcjogQXR0ZW1wdGluZyB0byBmZXRjaCB1c2VyIHJvbGUgZnJvbSBBUEkuLi4nKTtcbiAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hdXRoL2dldC11c2VyLXJvbGUnLCB7XG4gICAgICAgICAgICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgICAgICAgICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLCAvLyBJbmNsdWRlIGNvb2tpZXMgZm9yIE5leHRBdXRoIHNlc3Npb25cbiAgICAgICAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnQXV0aFByb3ZpZGVyOiBBUEkgcmVzcG9uc2Ugc3RhdHVzOicsIHJlc3BvbnNlLnN0YXR1cyk7XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgICAgICAgICBjb25zdCByb2xlRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgICAgICAgICBpZiAocm9sZURhdGEuc3VjY2Vzcykge1xuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coYEF1dGhQcm92aWRlcjogQVBJIHJldHVybmVkIHJvbGU6ICR7cm9sZURhdGEucm9sZX1gLCByb2xlRGF0YSk7XG4gICAgICAgICAgICAgICAgICBzZXRVc2VyUm9sZShyb2xlRGF0YS5yb2xlKTtcbiAgICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd1c2VyX3JvbGUnLCByb2xlRGF0YS5yb2xlKTtcbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgLy8gU2V0IGN1cnJlbnQgdmlldyByb2xlIGJhc2VkIG9uIHRoZSBjb21wcmVoZW5zaXZlIHJvbGVcbiAgICAgICAgICAgICAgICAgIGlmIChyb2xlRGF0YS5yb2xlID09PSAnYm90aCcpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gRm9yIGR1YWwtcm9sZSB1c2VycywgY2hlY2sgaWYgdGhlcmUncyBhIHByZWZlcnJlZCB2aWV3IHN0b3JlZCBvciBkZWZhdWx0IHRvIHBhcmVudFxuICAgICAgICAgICAgICAgICAgICBjb25zdCBzdG9yZWRWaWV3Um9sZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdjdXJyZW50X3ZpZXdfcm9sZScpIGFzICdwYXJlbnQnIHwgJ29wZXJhdG9yJyB8IG51bGw7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHByZWZlcnJlZFZpZXcgPSBzdG9yZWRWaWV3Um9sZSB8fCAncGFyZW50JztcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3VycmVudFZpZXdSb2xlKHByZWZlcnJlZFZpZXcpO1xuICAgICAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnY3VycmVudF92aWV3X3JvbGUnLCBwcmVmZXJyZWRWaWV3KTtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coYEF1dGhQcm92aWRlcjogU2V0IHZpZXcgcm9sZSB0byAke3ByZWZlcnJlZFZpZXd9IGZvciBkdWFsLXJvbGUgdXNlcmApO1xuICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChyb2xlRGF0YS5yb2xlID09PSAncGFyZW50JyB8fCByb2xlRGF0YS5yb2xlID09PSAnb3BlcmF0b3InKSB7XG4gICAgICAgICAgICAgICAgICAgIHNldEN1cnJlbnRWaWV3Um9sZShyb2xlRGF0YS5yb2xlIGFzICdwYXJlbnQnIHwgJ29wZXJhdG9yJyk7XG4gICAgICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdjdXJyZW50X3ZpZXdfcm9sZScsIHJvbGVEYXRhLnJvbGUpO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnQXV0aFByb3ZpZGVyOiBBUEkgcmV0dXJuZWQgdW5zdWNjZXNzZnVsIHJlc3BvbnNlOicsIHJvbGVEYXRhKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ0F1dGhQcm92aWRlcjogQVBJIHJldHVybmVkIGVycm9yIHN0YXR1czonLCByZXNwb25zZS5zdGF0dXMpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAvLyBGYWxsYmFjayB0byBzZXNzaW9uIHJvbGUgaWYgQVBJIGZhaWxzXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdBdXRoUHJvdmlkZXI6IEFQSSBmYWlsZWQsIHVzaW5nIHNlc3Npb24gcm9sZSBhcyBmYWxsYmFjaycpO1xuICAgICAgICAgICAgICBpZiAoc2Vzc2lvblVzZXI/LnJvbGUpIHtcbiAgICAgICAgICAgICAgICBzZXRVc2VyUm9sZShzZXNzaW9uVXNlci5yb2xlKTtcbiAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndXNlcl9yb2xlJywgc2Vzc2lvblVzZXIucm9sZSk7XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgLy8gU2V0IHZpZXcgcm9sZSBmb3Igc2Vzc2lvbiBmYWxsYmFja1xuICAgICAgICAgICAgICAgIGlmIChzZXNzaW9uVXNlci5yb2xlID09PSAnYm90aCcpIHtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IHN0b3JlZFZpZXdSb2xlID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2N1cnJlbnRfdmlld19yb2xlJykgYXMgJ3BhcmVudCcgfCAnb3BlcmF0b3InIHwgbnVsbDtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IHByZWZlcnJlZFZpZXcgPSBzdG9yZWRWaWV3Um9sZSB8fCAncGFyZW50JztcbiAgICAgICAgICAgICAgICAgIHNldEN1cnJlbnRWaWV3Um9sZShwcmVmZXJyZWRWaWV3KTtcbiAgICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdjdXJyZW50X3ZpZXdfcm9sZScsIHByZWZlcnJlZFZpZXcpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoc2Vzc2lvblVzZXIucm9sZSA9PT0gJ3BhcmVudCcgfHwgc2Vzc2lvblVzZXIucm9sZSA9PT0gJ29wZXJhdG9yJykge1xuICAgICAgICAgICAgICAgICAgc2V0Q3VycmVudFZpZXdSb2xlKHNlc3Npb25Vc2VyLnJvbGUgYXMgJ3BhcmVudCcgfCAnb3BlcmF0b3InKTtcbiAgICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdjdXJyZW50X3ZpZXdfcm9sZScsIHNlc3Npb25Vc2VyLnJvbGUpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignQXV0aFByb3ZpZGVyOiBFcnJvciBmZXRjaGluZyB1c2VyIHJvbGU6JywgZXJyb3IpO1xuICAgICAgICAgICAgICAvLyBGYWxsYmFjayB0byBzZXNzaW9uIHJvbGUgb24gbmV0d29yayBlcnJvclxuICAgICAgICAgICAgICBpZiAoc2Vzc2lvblVzZXI/LnJvbGUpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnQXV0aFByb3ZpZGVyOiBVc2luZyBzZXNzaW9uIHJvbGUgYXMgZmFsbGJhY2sgZHVlIHRvIGVycm9yJyk7XG4gICAgICAgICAgICAgICAgc2V0VXNlclJvbGUoc2Vzc2lvblVzZXIucm9sZSk7XG4gICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3VzZXJfcm9sZScsIHNlc3Npb25Vc2VyLnJvbGUpO1xuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIC8vIFNldCB2aWV3IHJvbGUgZm9yIGVycm9yIGZhbGxiYWNrXG4gICAgICAgICAgICAgICAgaWYgKHNlc3Npb25Vc2VyLnJvbGUgPT09ICdib3RoJykge1xuICAgICAgICAgICAgICAgICAgY29uc3Qgc3RvcmVkVmlld1JvbGUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY3VycmVudF92aWV3X3JvbGUnKSBhcyAncGFyZW50JyB8ICdvcGVyYXRvcicgfCBudWxsO1xuICAgICAgICAgICAgICAgICAgY29uc3QgcHJlZmVycmVkVmlldyA9IHN0b3JlZFZpZXdSb2xlIHx8ICdwYXJlbnQnO1xuICAgICAgICAgICAgICAgICAgc2V0Q3VycmVudFZpZXdSb2xlKHByZWZlcnJlZFZpZXcpO1xuICAgICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2N1cnJlbnRfdmlld19yb2xlJywgcHJlZmVycmVkVmlldyk7XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmIChzZXNzaW9uVXNlci5yb2xlID09PSAncGFyZW50JyB8fCBzZXNzaW9uVXNlci5yb2xlID09PSAnb3BlcmF0b3InKSB7XG4gICAgICAgICAgICAgICAgICBzZXRDdXJyZW50Vmlld1JvbGUoc2Vzc2lvblVzZXIucm9sZSBhcyAncGFyZW50JyB8ICdvcGVyYXRvcicpO1xuICAgICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2N1cnJlbnRfdmlld19yb2xlJywgc2Vzc2lvblVzZXIucm9sZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfTtcbiAgICAgICAgICBcbiAgICAgICAgICBmZXRjaFVzZXJSb2xlKCk7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAvLyBDaGVjayBmb3Igcm9sZSBpbiBsb2NhbFN0b3JhZ2VcbiAgICAgICAgY29uc3Qgc3RvcmVkUm9sZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VyX3JvbGUnKTtcbiAgICAgICAgXG4gICAgICAgIGlmIChzdG9yZWRSb2xlKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coYEF1dGhQcm92aWRlcjogRm91bmQgcm9sZSBpbiBsb2NhbFN0b3JhZ2U6ICR7c3RvcmVkUm9sZX1gKTtcbiAgICAgICAgICBzZXRVc2VyUm9sZShzdG9yZWRSb2xlIGFzIGFueSk7XG4gICAgICAgICAgXG4gICAgICAgICAgLy8gSWYgdGhpcyBpcyBhIHN0dWRlbnQsIGNoZWNrIGZvciBzdHVkZW50IElEXG4gICAgICAgICAgaWYgKHN0b3JlZFJvbGUgPT09ICdzdHVkZW50Jykge1xuICAgICAgICAgICAgY29uc3Qgc3RvcmVkU3R1ZGVudElkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3N0dWRlbnRfaWQnKSB8fCBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY3VycmVudF9zZXNzaW9uJyk7XG4gICAgICAgICAgICBpZiAoc3RvcmVkU3R1ZGVudElkKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBBdXRoUHJvdmlkZXI6IEZvdW5kIHN0dWRlbnQgSUQgaW4gbG9jYWxTdG9yYWdlOiAke3N0b3JlZFN0dWRlbnRJZH1gKTtcbiAgICAgICAgICAgICAgc2V0U3R1ZGVudFNlc3Npb24oc3RvcmVkU3R1ZGVudElkKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSBpZiAodXNlcikge1xuICAgICAgICAgIC8vIEZpcmViYXNlIHVzZXIgc3BlY2lmaWMgbG9naWNcbiAgICAgICAgICAvLyBDaGVjayBmb3IgcGFyZW50IGluZGljYXRvcnNcbiAgICAgICAgICBjb25zdCBpc1BhcmVudCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdwYXJlbnRfaWQnKSB8fCBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgncGFyZW50X25hbWUnKTtcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBDaGVjayBmb3IgdGVtcG9yYXJ5IHN0dWRlbnQgc2Vzc2lvbiAocGFyZW50IHZpZXdpbmcgc3R1ZGVudCBkYXNoYm9hcmQpXG4gICAgICAgICAgY29uc3QgdGVtcFN0dWRlbnRTZXNzaW9uID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3RlbXBfc3R1ZGVudF9zZXNzaW9uJyk7XG4gICAgICAgICAgXG4gICAgICAgICAgaWYgKGlzUGFyZW50KSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcIkF1dGhQcm92aWRlcjogVXNlciBhcHBlYXJzIHRvIGJlIGEgcGFyZW50IGJhc2VkIG9uIGxvY2FsU3RvcmFnZVwiKTtcbiAgICAgICAgICAgIHNldFVzZXJSb2xlKCdwYXJlbnQnKTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgLy8gSWYgcGFyZW50IGlzIHZpZXdpbmcgYSBzdHVkZW50IGRhc2hib2FyZCwgc2V0IHRoZSBzdHVkZW50IHNlc3Npb25cbiAgICAgICAgICAgIGlmICh0ZW1wU3R1ZGVudFNlc3Npb24pIHtcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coYEF1dGhQcm92aWRlcjogUGFyZW50IHZpZXdpbmcgc3R1ZGVudCBkYXNoYm9hcmQgZm9yOiAke3RlbXBTdHVkZW50U2Vzc2lvbn1gKTtcbiAgICAgICAgICAgICAgc2V0U3R1ZGVudFNlc3Npb24odGVtcFN0dWRlbnRTZXNzaW9uKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgLy8gRGVmYXVsdCB0byBzdHVkZW50IHJvbGUgaWYgbm8gb3RoZXIgaW5kaWNhdG9yc1xuICAgICAgICAgICAgY29uc29sZS5sb2coXCJBdXRoUHJvdmlkZXI6IE5vIHJvbGUgaW5kaWNhdG9ycyBmb3VuZCwgZGVmYXVsdGluZyB0byBzdHVkZW50XCIpO1xuICAgICAgICAgICAgc2V0VXNlclJvbGUoJ3N0dWRlbnQnKTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgLy8gVHJ5IHRvIGZpbmQgc3R1ZGVudCBJRFxuICAgICAgICAgICAgY29uc3Qgc3RvcmVkU3R1ZGVudElkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3N0dWRlbnRfaWQnKSB8fCBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY3VycmVudF9zZXNzaW9uJyk7XG4gICAgICAgICAgICBpZiAoc3RvcmVkU3R1ZGVudElkKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBBdXRoUHJvdmlkZXI6IEZvdW5kIHN0dWRlbnQgSUQ6ICR7c3RvcmVkU3R1ZGVudElkfWApO1xuICAgICAgICAgICAgICBzZXRTdHVkZW50U2Vzc2lvbihzdG9yZWRTdHVkZW50SWQpO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coYEF1dGhQcm92aWRlcjogTm8gc3R1ZGVudCBJRCBmb3VuZCwgdXNpbmcgVUlEOiAke3VzZXIudWlkfWApO1xuICAgICAgICAgICAgICBzZXRTdHVkZW50U2Vzc2lvbih1c2VyLnVpZCk7XG4gICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdzdHVkZW50X2lkJywgdXNlci51aWQpO1xuICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnY3VycmVudF9zZXNzaW9uJywgdXNlci51aWQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgLy8gRW5kIG9mIHJvbGUgZGV0ZXJtaW5hdGlvbiBsb2dpY1xuICAgIH1cbiAgfSwgW2xvYWRpbmcsIHVzZXIsIHVzZXJSb2xlLCBzZXRVc2VyUm9sZSwgc2V0U3R1ZGVudFNlc3Npb24sIHN0YXR1cywgc2Vzc2lvbl0pO1xuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gICAgLy8gU0VDT05EIHVzZUVmZmVjdCDigJMgTmF2aWdhdGlvbiAvIFJlZGlyZWN0aW9uIGxvZ2ljXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAgICAvLyBSRVZJU0VEIE5hdmlnYXRpb24gLyBSZWRpcmVjdGlvbiBlZmZlY3RcbiAgICAvLyBUaGlzIHJlcGxhY2VzIHRoZSBvbGQsIGNvbXBsZXggdXNlRWZmZWN0IHdpdGggYSBzaW1wbGVyLCBtb3JlIGRlY2xhcmF0aXZlIGFwcHJvYWNoXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIC8vIFdhaXQgdW50aWwgYXV0aCBzdGF0ZSAmIE5leHQtQXV0aCBzZXNzaW9uIGhhdmUgZmluaXNoZWQgbG9hZGluZ1xuICAgICAgaWYgKGxvYWRpbmcgfHwgc3RhdHVzID09PSAnbG9hZGluZycpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0F1dGhQcm92aWRlciBOYXY6IFdhaXRpbmcgZm9yIGF1dGggdG8gY29tcGxldGUuLi4nLCB7IGxvYWRpbmcsIHN0YXR1cyB9KTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBjdXJyZW50UGF0aCA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gd2luZG93LmxvY2F0aW9uLnBhdGhuYW1lIDogJyc7XG4gICAgICBjb25zdCBzZXNzaW9uUm9sZSA9IChzZXNzaW9uPy51c2VyIGFzIFNlc3Npb25Vc2VyKT8ucm9sZTtcbiAgICAgIGNvbnN0IGlzTmV4dEF1dGhBdXRoZW50aWNhdGVkID0gc3RhdHVzID09PSAnYXV0aGVudGljYXRlZCcgJiYgISFzZXNzaW9uUm9sZTtcbiAgICAgIGNvbnN0IGhhc1Nlc3Npb25XaXRoUm9sZSA9ICEhc2Vzc2lvbiAmJiAhIShzZXNzaW9uIGFzIGFueSk/LnVzZXI/LnJvbGU7XG4gICAgICBcbiAgICAgIGNvbnNvbGUubG9nKCdBdXRoUHJvdmlkZXIgTmF2OiBERUJVRycsIHsgXG4gICAgICAgIGN1cnJlbnRQYXRoLCBcbiAgICAgICAgbG9hZGluZyxcbiAgICAgICAgc3RhdHVzLFxuICAgICAgICBpc05leHRBdXRoQXV0aGVudGljYXRlZCwgXG4gICAgICAgIGhhc1Nlc3Npb25XaXRoUm9sZSxcbiAgICAgICAgc2Vzc2lvblJvbGUsIFxuICAgICAgICBjdXJyZW50Vmlld1JvbGUsXG4gICAgICAgIHNlc3Npb246ICEhc2Vzc2lvbixcbiAgICAgICAgc2Vzc2lvblVzZXI6IHNlc3Npb24/LnVzZXIsXG4gICAgICAgIGZpcmViYXNlVXNlcjogISF1c2VyLFxuICAgICAgICBzZXNzaW9uVXNlclJvbGU6IChzZXNzaW9uPy51c2VyIGFzIGFueSk/LnJvbGUsXG4gICAgICAgIHJhd1Nlc3Npb246IHNlc3Npb25cbiAgICAgIH0pO1xuXG4gICAgICAvLyAtLS0gMS4gSGFuZGxlIFVuYXV0aGVudGljYXRlZCBVc2VycyAtLS1cbiAgICAgIC8vIFVwZGF0ZWQgYXV0aGVudGljYXRpb24gY2hlY2sgdG8gYmUgbW9yZSByb2J1c3RcbiAgICAgIGNvbnN0IGlzUmVhbGx5QXV0aGVudGljYXRlZCA9IGlzTmV4dEF1dGhBdXRoZW50aWNhdGVkIHx8IGhhc1Nlc3Npb25XaXRoUm9sZTtcbiAgICAgIFxuICAgICAgY29uc29sZS5sb2coJ0F1dGhQcm92aWRlciBOYXY6IEF1dGhlbnRpY2F0aW9uIHN0YXR1czonLCB7XG4gICAgICAgIGlzTmV4dEF1dGhBdXRoZW50aWNhdGVkLFxuICAgICAgICBoYXNTZXNzaW9uV2l0aFJvbGUsXG4gICAgICAgIGlzUmVhbGx5QXV0aGVudGljYXRlZCxcbiAgICAgICAgc3RhdHVzQ2hlY2s6IHN0YXR1cyA9PT0gJ2F1dGhlbnRpY2F0ZWQnLFxuICAgICAgICBzZXNzaW9uUm9sZUNoZWNrOiAhIXNlc3Npb25Sb2xlLFxuICAgICAgICBoYXNTZXNzaW9uQ2hlY2s6ICEhc2Vzc2lvbixcbiAgICAgICAgc2Vzc2lvblVzZXJSb2xlQ2hlY2s6ICEhKHNlc3Npb24gYXMgYW55KT8udXNlcj8ucm9sZVxuICAgICAgfSk7XG4gICAgICBcbiAgICAgIC8vIFNQRUNJQUwgQ0FTRTogSWYgd2UncmUgb24gYW55IGRhc2hib2FyZCBwYXRoIGFuZCBoYXZlIGFueSBzZXNzaW9uLCBkb24ndCByZWRpcmVjdFxuICAgICAgLy8gRVhDRVBUIGZvciBkdWFsLXJvbGUgdXNlcnMgd2hvIG5lZWQgbmF2aWdhdGlvbiBiZXR3ZWVuIGRhc2hib2FyZHNcbiAgICAgIGlmICgoY3VycmVudFBhdGguc3RhcnRzV2l0aCgnL29wZXJhdG9yJykgfHwgY3VycmVudFBhdGggPT09ICcvZGFzaGJvYXJkJyB8fCBjdXJyZW50UGF0aC5zdGFydHNXaXRoKCcvZGFzaGJvYXJkLycpKSAmJiBzZXNzaW9uICYmIChzZXNzaW9uIGFzIGFueSk/LnVzZXI/LnJvbGUpIHtcbiAgICAgICAgLy8gQWxsb3cgZHVhbC1yb2xlIG5hdmlnYXRpb24gdG8gcHJvY2VlZFxuICAgICAgICBpZiAoc2Vzc2lvblJvbGUgPT09ICdib3RoJykge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdBdXRoUHJvdmlkZXIgTmF2OiBPbiBkYXNoYm9hcmQgcGF0aCB3aXRoIGR1YWwtcm9sZSBzZXNzaW9uLCBhbGxvd2luZyBkdWFsLXJvbGUgbmF2aWdhdGlvbiBsb2dpYycpO1xuICAgICAgICAgIC8vIERvbid0IHJldHVybiBoZXJlIC0gbGV0IGR1YWwtcm9sZSBuYXZpZ2F0aW9uIGxvZ2ljIHJ1blxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdBdXRoUHJvdmlkZXIgTmF2OiBPbiBkYXNoYm9hcmQgcGF0aCB3aXRoIHZhbGlkIHNlc3Npb24sIGFsbG93aW5nIGFjY2VzcycpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgXG4gICAgICBpZiAoIWlzUmVhbGx5QXV0aGVudGljYXRlZCkge1xuICAgICAgICBjb25zdCBwdWJsaWNQYXRocyA9IFsnL2xvZ2luJywgJy9yZWdpc3RlcicsICcvZm9yZ290LXBhc3N3b3JkJywgJy9yZXNldCddO1xuICAgICAgICAvLyBJZiBub3Qgb24gYSBwdWJsaWMgcGF0aCwgcmVkaXJlY3QgdG8gbG9naW4uXG4gICAgICAgIGlmICghcHVibGljUGF0aHMuc29tZShwID0+IGN1cnJlbnRQYXRoLnN0YXJ0c1dpdGgocCkpKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coYEF1dGhQcm92aWRlciBOYXY6IE5vdCBhdXRoZW50aWNhdGVkLCByZWRpcmVjdGluZyBmcm9tICR7Y3VycmVudFBhdGh9IHRvIC9sb2dpbmApO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdBdXRoUHJvdmlkZXIgTmF2OiBSZWRpcmVjdCByZWFzb24gLSBhdXRoZW50aWNhdGlvbiBjaGVjayBmYWlsZWQnLCB7XG4gICAgICAgICAgICBjdXJyZW50UGF0aCxcbiAgICAgICAgICAgIGlzTmV4dEF1dGhBdXRoZW50aWNhdGVkLFxuICAgICAgICAgICAgaGFzU2Vzc2lvbldpdGhSb2xlLFxuICAgICAgICAgICAgc2Vzc2lvbjogc2Vzc2lvbixcbiAgICAgICAgICAgIHNlc3Npb25Vc2VyOiBzZXNzaW9uPy51c2VyLFxuICAgICAgICAgICAgc3RhdHVzLFxuICAgICAgICAgICAgc2Vzc2lvblJvbGVcbiAgICAgICAgICB9KTtcbiAgICAgICAgICByb3V0ZXIucmVwbGFjZSgnL2xvZ2luJyk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0F1dGhQcm92aWRlciBOYXY6IE9uIHB1YmxpYyBwYXRoLCBubyByZWRpcmVjdCBuZWVkZWQnKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBcbiAgICAgIGNvbnNvbGUubG9nKCdBdXRoUHJvdmlkZXIgTmF2OiBVc2VyIGlzIGF1dGhlbnRpY2F0ZWQsIHByb2NlZWRpbmcgd2l0aCBuYXZpZ2F0aW9uIGxvZ2ljJyk7XG4gICAgICBcbiAgICAgIC8vIC0tLSAyLiBIYW5kbGUgQXV0aGVudGljYXRlZCBVc2VycyAtLS1cblxuICAgICAgLy8gSWYgdXNlciBpcyBvbiB0aGUgbG9naW4gcGFnZSwgZ2V0IHRoZW0gb3V0IG9mIHRoZXJlLlxuICAgICAgaWYgKGN1cnJlbnRQYXRoLnN0YXJ0c1dpdGgoJy9sb2dpbicpKSB7XG4gICAgICAgIC8vIEZvciBkdWFsLXJvbGUgdXNlcnMsIGdvIHRvIHRoZSBkYXNoYm9hcmQgbWF0Y2hpbmcgdGhlaXIgc2VsZWN0ZWQgdmlldy5cbiAgICAgICAgY29uc3QgZGVzdGluYXRpb24gPSBzZXNzaW9uUm9sZSA9PT0gJ2JvdGgnIFxuICAgICAgICAgID8gKGN1cnJlbnRWaWV3Um9sZSA9PT0gJ29wZXJhdG9yJyA/ICcvb3BlcmF0b3IvZGFzaGJvYXJkJyA6ICcvZGFzaGJvYXJkJylcbiAgICAgICAgICA6ICcvZGFzaGJvYXJkJzsgLy8gRGVmYXVsdCBmb3IgcGFyZW50c1xuICAgICAgICBcbiAgICAgICAgY29uc29sZS5sb2coYEF1dGhQcm92aWRlciBOYXY6IEF1dGhlbnRpY2F0ZWQgb24gbG9naW4gcGFnZSwgcmVkaXJlY3RpbmcgdG8gJHtkZXN0aW5hdGlvbn1gKTtcbiAgICAgICAgcm91dGVyLnJlcGxhY2UoZGVzdGluYXRpb24pO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIC0tLSAzLiBIYW5kbGUgRHVhbC1Sb2xlIE5hdmlnYXRpb24gLS0tXG4gICAgICAvLyBUaGlzIGlzIHRoZSBkZWNsYXJhdGl2ZSBuYXZpZ2F0aW9uIHRoYXQgcmVwbGFjZXMgdGhlIHNldFRpbWVvdXQgbG9naWMuXG4gICAgICBpZiAoc2Vzc2lvblJvbGUgPT09ICdib3RoJykge1xuICAgICAgICAvLyBJZiB0aGUgdXNlcidzIGludGVudCAoY3VycmVudFZpZXdSb2xlKSBkb2Vzbid0IG1hdGNoIHRoZWlyIGxvY2F0aW9uLCBuYXZpZ2F0ZSB0aGVtLlxuICAgICAgICBpZiAoY3VycmVudFZpZXdSb2xlID09PSAnb3BlcmF0b3InICYmICFjdXJyZW50UGF0aC5zdGFydHNXaXRoKCcvb3BlcmF0b3InKSkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKFwiQXV0aFByb3ZpZGVyIE5hdjogRHVhbC1yb2xlIHVzZXIgd2FudHMgJ29wZXJhdG9yJyB2aWV3LCBuYXZpZ2F0aW5nIHRvIG9wZXJhdG9yIGRhc2hib2FyZC5cIik7XG4gICAgICAgICAgcm91dGVyLnJlcGxhY2UoJy9vcGVyYXRvci9kYXNoYm9hcmQnKTtcbiAgICAgICAgICByZXR1cm47IC8vIFN0b3AgZnVydGhlciBwcm9jZXNzaW5nIGFmdGVyIG5hdmlnYXRpb25cbiAgICAgICAgfVxuICAgICAgICBpZiAoY3VycmVudFZpZXdSb2xlID09PSAncGFyZW50JyAmJiBjdXJyZW50UGF0aC5zdGFydHNXaXRoKCcvb3BlcmF0b3InKSkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKFwiQXV0aFByb3ZpZGVyIE5hdjogRHVhbC1yb2xlIHVzZXIgd2FudHMgJ3BhcmVudCcgdmlldywgbmF2aWdhdGluZyB0byBwYXJlbnQgZGFzaGJvYXJkLlwiKTtcbiAgICAgICAgICByb3V0ZXIucmVwbGFjZSgnL2Rhc2hib2FyZCcpO1xuICAgICAgICAgIHJldHVybjsgLy8gU3RvcCBmdXJ0aGVyIHByb2Nlc3NpbmcgYWZ0ZXIgbmF2aWdhdGlvblxuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAvLyBJZiBhIGR1YWwtcm9sZSB1c2VyIGlzIG9uIGVpdGhlciBvZiB0aGVpciB2YWxpZCBkYXNoYm9hcmRzLCBETyBOT1RISU5HLiBMZXQgdGhlbSBiZS5cbiAgICAgICAgY29uc29sZS5sb2coXCJBdXRoUHJvdmlkZXIgTmF2OiBEdWFsLXJvbGUgdXNlciBvbiBhIHZhbGlkIHBhdGgsIG5vIHJlZGlyZWN0IG5lZWRlZC5cIik7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gLS0tIDQuIEhhbmRsZSBTaW5nbGUtUm9sZSBVc2VycyBvbiBXcm9uZyBQYWdlIC0tLVxuICAgICAgLy8gVGhpcyBsb2dpYyBub3cgb25seSBhcHBsaWVzIHRvIHVzZXJzIHdobyBhcmUgTk9UIGR1YWwtcm9sZS5cbiAgICAgIGlmIChzZXNzaW9uUm9sZSA9PT0gJ3BhcmVudCcgJiYgY3VycmVudFBhdGguc3RhcnRzV2l0aCgnL29wZXJhdG9yJykpIHtcbiAgICAgICAgY29uc29sZS5sb2coYEF1dGhQcm92aWRlciBOYXY6IFBhcmVudCBvbiBvcGVyYXRvciBwYWdlLCByZWRpcmVjdGluZyB0byAvZGFzaGJvYXJkLmApO1xuICAgICAgICByb3V0ZXIucmVwbGFjZSgnL2Rhc2hib2FyZCcpO1xuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBOb3RlOiBBIHNpbmdsZS1yb2xlICdvcGVyYXRvcicgd291bGQgYmUgaGFuZGxlZCBieSB0aGUgbG9naWMgaW4gc3RlcCAyIGlmIHRoZXkgbGFuZCBvbiAnLycuXG4gICAgICAvLyBUaGlzIGNvdmVycyBhbGwgY3JpdGljYWwgY2FzZXMuXG5cbiAgICB9LCBbbG9hZGluZywgc3RhdHVzLCBzZXNzaW9uLCBjdXJyZW50Vmlld1JvbGUsIHJvdXRlcl0pOyAvLyBEZXBlbmRlbmNpZXMgdGhhdCBkcml2ZSBuYXZpZ2F0aW9uXG5cbnJldHVybiAoXG4gIDxBdXRoQ29udGV4dC5Qcm92aWRlclxuICAgIHZhbHVlPXt7XG4gICAgICB1c2VyLFxuICAgICAgdXNlckRhdGEsXG4gICAgICBjaGlsZHJlbkRhdGEsXG4gICAgICBsb2FkaW5nLFxuICAgICAgZXJyb3IsXG4gICAgICBpc0F1dGhlbnRpY2F0ZWQ6ICghIXVzZXIgJiYgISF1c2VyLnVpZCkgfHwgc3RhdHVzID09PSAnYXV0aGVudGljYXRlZCcgfHwgKCEhc2Vzc2lvbiAmJiAhIShzZXNzaW9uIGFzIGFueSk/LnVzZXI/LnJvbGUpLFxuICAgICAgLy8gVXNlIFNlc3Npb25Vc2VyIHR5cGUgZm9yIGZhbGxiYWNrc1xuICAgICAgc3R1ZGVudFNlc3Npb246IHN0dWRlbnRTZXNzaW9uIHx8ICgoc2Vzc2lvbj8udXNlciBhcyBTZXNzaW9uVXNlcik/LnJvbGUgPT09ICdzdHVkZW50JyA/IChzZXNzaW9uPy51c2VyIGFzIFNlc3Npb25Vc2VyKT8uaWQgOiBudWxsKSxcbiAgICAgIHVzZXJSb2xlOiAoc2Vzc2lvbj8udXNlciBhcyBTZXNzaW9uVXNlcik/LnJvbGUgfHwgdXNlckRhdGE/LnJvbGUgfHwgdXNlclJvbGUgfHwgbnVsbCxcbiAgICAgIGxvY2FsRWZmZWN0aXZlUm9sZTogZ2V0RWZmZWN0aXZlUm9sZSgpLFxuICAgICAgaXNQYXJlbnQ6ICgoc2Vzc2lvbj8udXNlciBhcyBTZXNzaW9uVXNlcik/LnJvbGUgfHwgdXNlckRhdGE/LnJvbGUgfHwgdXNlclJvbGUpID09PSAncGFyZW50JyB8fCBcbiAgICAgICAgICAgICAgICAoKHNlc3Npb24/LnVzZXIgYXMgU2Vzc2lvblVzZXIpPy5yb2xlIHx8IHVzZXJEYXRhPy5yb2xlIHx8IHVzZXJSb2xlKSA9PT0gJ2JvdGgnLFxuICAgICAgaXNPcGVyYXRvcjogKChzZXNzaW9uPy51c2VyIGFzIFNlc3Npb25Vc2VyKT8ucm9sZSB8fCB1c2VyRGF0YT8ucm9sZSB8fCB1c2VyUm9sZSkgPT09ICdvcGVyYXRvcicgfHwgXG4gICAgICAgICAgICAgICAgICAoKHNlc3Npb24/LnVzZXIgYXMgU2Vzc2lvblVzZXIpPy5yb2xlIHx8IHVzZXJEYXRhPy5yb2xlIHx8IHVzZXJSb2xlKSA9PT0gJ2JvdGgnLFxuICAgICAgY3VycmVudFZpZXdSb2xlLFxuICAgICAgc2V0Q3VycmVudFZpZXdSb2xlOiB1cGRhdGVDdXJyZW50Vmlld1JvbGUsXG4gICAgICBtYW51YWxTeW5jV2l0aExvY2FsU3RvcmFnZSxcbiAgICAgIHJlZnJlc2hVc2VyRGF0YSxcbiAgICAgIHJlZnJlc2hDaGlsZHJlbkRhdGEsXG4gICAgICBoYW5kbGVMb2dpblN1Y2Nlc3MsICAgICAgICBsb2dvdXQ6IGFzeW5jICgpID0+IHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIkF1dGhQcm92aWRlcjogTG9nZ2luZyBvdXQuLi5cIik7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGF3YWl0IHNpZ25PdXQoYXV0aCk7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIENsZWFyIGJhY2tlbmQgcGVyZm9ybWFuY2UgY2FjaGVzXG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICBjb25zdCBjYWNoZVJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvY2xlYXItcGVyZm9ybWFuY2UtY2FjaGUnLCB7XG4gICAgICAgICAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgaWYgKGNhY2hlUmVzcG9uc2Uub2spIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcIkF1dGhQcm92aWRlcjogQmFja2VuZCBjYWNoZXMgY2xlYXJlZCBzdWNjZXNzZnVsbHlcIik7XG4gICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKFwiQXV0aFByb3ZpZGVyOiBGYWlsZWQgdG8gY2xlYXIgYmFja2VuZCBjYWNoZXMsIGJ1dCBjb250aW51aW5nIHdpdGggbG9nb3V0XCIpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGNhdGNoIChjYWNoZUVycm9yKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUud2FybihcIkF1dGhQcm92aWRlcjogRXJyb3IgY2xlYXJpbmcgYmFja2VuZCBjYWNoZXM6XCIsIGNhY2hlRXJyb3IpO1xuICAgICAgICAgICAgICAvLyBEb24ndCBmYWlsIGxvZ291dCBkdWUgdG8gY2FjaGUgY2xlYXJpbmcgaXNzdWVzXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIENsZWFyIGxvY2FsIHN0YXRlIGFuZCBzdG9yYWdlXG4gICAgICAgICAgICBzZXRVc2VyKG51bGwpO1xuICAgICAgICAgICAgc2V0VXNlckRhdGEobnVsbCk7XG4gICAgICAgICAgICBzZXRDaGlsZHJlbkRhdGEobnVsbCk7XG4gICAgICAgICAgICBzZXRVc2VyUm9sZShudWxsKTtcbiAgICAgICAgICAgIHNldFN0dWRlbnRTZXNzaW9uKG51bGwpO1xuICAgICAgICAgICAgc2V0Q3VycmVudFZpZXdSb2xlKG51bGwpO1xuICAgICAgICAgICAgc2V0SXNBdXRoZW50aWNhdGVkKGZhbHNlKTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgLy8gQ2xlYXIgYWxsIGJyb3dzZXIgc3RvcmFnZVxuICAgICAgICAgICAgbG9jYWxTdG9yYWdlLmNsZWFyKCk7XG4gICAgICAgICAgICBzZXNzaW9uU3RvcmFnZS5jbGVhcigpO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICAvLyBDbGVhciBJbmRleGVkREIgKGlmIGFueSBkYXRhIGlzIHN0b3JlZCB0aGVyZSlcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIGlmICgnaW5kZXhlZERCJyBpbiB3aW5kb3cpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhYmFzZXMgPSBhd2FpdCBpbmRleGVkREIuZGF0YWJhc2VzKCk7XG4gICAgICAgICAgICAgICAgZGF0YWJhc2VzLmZvckVhY2goYXN5bmMgKGRiKSA9PiB7XG4gICAgICAgICAgICAgICAgICBpZiAoZGIubmFtZSkge1xuICAgICAgICAgICAgICAgICAgICBpbmRleGVkREIuZGVsZXRlRGF0YWJhc2UoZGIubmFtZSk7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gY2F0Y2ggKGlkYkVycm9yKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUud2FybihcIkF1dGhQcm92aWRlcjogRXJyb3IgY2xlYXJpbmcgSW5kZXhlZERCOlwiLCBpZGJFcnJvcik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIENsZWFyIHNlcnZpY2Ugd29ya2VyIGNhY2hlc1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgaWYgKCdjYWNoZXMnIGluIHdpbmRvdykge1xuICAgICAgICAgICAgICAgIGNvbnN0IGNhY2hlTmFtZXMgPSBhd2FpdCBjYWNoZXMua2V5cygpO1xuICAgICAgICAgICAgICAgIGF3YWl0IFByb21pc2UuYWxsKFxuICAgICAgICAgICAgICAgICAgY2FjaGVOYW1lcy5tYXAoY2FjaGVOYW1lID0+IGNhY2hlcy5kZWxldGUoY2FjaGVOYW1lKSlcbiAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiQXV0aFByb3ZpZGVyOiBTZXJ2aWNlIHdvcmtlciBjYWNoZXMgY2xlYXJlZFwiKTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBjYXRjaCAoc3dFcnJvcikge1xuICAgICAgICAgICAgICBjb25zb2xlLndhcm4oXCJBdXRoUHJvdmlkZXI6IEVycm9yIGNsZWFyaW5nIHNlcnZpY2Ugd29ya2VyIGNhY2hlczpcIiwgc3dFcnJvcik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiQXV0aFByb3ZpZGVyOiBMb2dvdXQgc3VjY2Vzc2Z1bC4gQWxsIGNhY2hlcyBjbGVhcmVkLiBSZWRpcmVjdGluZyB0byBsb2dpbi5cIik7XG4gICAgICAgICAgICByb3V0ZXIucHVzaCgnL2xvZ2luJyk7XG4gICAgICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiQXV0aFByb3ZpZGVyOiBMb2dvdXQgZmFpbGVkOlwiLCBlcnIpO1xuICAgICAgICAgICAgc2V0RXJyb3IoXCJMb2dvdXQgZmFpbGVkLiBQbGVhc2UgdHJ5IGFnYWluLlwiKTtcbiAgICAgICAgICAgIC8vIFN0aWxsIGF0dGVtcHQgdG8gY2xlYXIgc3RhdGUvc3RvcmFnZSBldmVuIGlmIHNpZ25PdXQgZmFpbHNcbiAgICAgICAgICAgIHNldFVzZXIobnVsbCk7XG4gICAgICAgICAgICBzZXRVc2VyRGF0YShudWxsKTtcbiAgICAgICAgICAgIHNldENoaWxkcmVuRGF0YShudWxsKTtcbiAgICAgICAgICAgIHNldFVzZXJSb2xlKG51bGwpO1xuICAgICAgICAgICAgc2V0U3R1ZGVudFNlc3Npb24obnVsbCk7XG4gICAgICAgICAgICBzZXRDdXJyZW50Vmlld1JvbGUobnVsbCk7XG4gICAgICAgICAgICBzZXRJc0F1dGhlbnRpY2F0ZWQoZmFsc2UpO1xuICAgICAgICAgICAgbG9jYWxTdG9yYWdlLmNsZWFyKCk7XG4gICAgICAgICAgICBzZXNzaW9uU3RvcmFnZS5jbGVhcigpO1xuICAgICAgICAgICAgcm91dGVyLnB1c2goJy9sb2dpbicpOyAvLyBSZWRpcmVjdCBldmVuIG9uIGVycm9yXG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgfX0gLy8gQ29ycmVjdGVkIGNsb3NpbmcgYnJhY2VzIGZvciB2YWx1ZSBvYmplY3RcbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9BdXRoQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn1cblxuZXhwb3J0IGNvbnN0IHVzZUF1dGggPSAoKSA9PiB1c2VDb250ZXh0KEF1dGhDb250ZXh0KTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VDYWxsYmFjayIsInVzZVJlZiIsIm9uQXV0aFN0YXRlQ2hhbmdlZCIsInNpZ25JbldpdGhDdXN0b21Ub2tlbiIsInNpZ25PdXQiLCJhdXRoIiwiZGIiLCJ1c2VSb3V0ZXIiLCJkb2MiLCJ1cGRhdGVEb2MiLCJ1c2VTZXNzaW9uIiwiQXV0aENvbnRleHQiLCJpc0F1dGhlbnRpY2F0ZWQiLCJzdHVkZW50U2Vzc2lvbiIsInVzZXJSb2xlIiwibG9jYWxFZmZlY3RpdmVSb2xlIiwiaXNQYXJlbnQiLCJpc09wZXJhdG9yIiwiY3VycmVudFZpZXdSb2xlIiwic2V0Q3VycmVudFZpZXdSb2xlIiwibWFudWFsU3luY1dpdGhMb2NhbFN0b3JhZ2UiLCJ1c2VyIiwidXNlckRhdGEiLCJjaGlsZHJlbkRhdGEiLCJsb2FkaW5nIiwiZXJyb3IiLCJyZWZyZXNoVXNlckRhdGEiLCJyZWZyZXNoQ2hpbGRyZW5EYXRhIiwiaGFuZGxlTG9naW5TdWNjZXNzIiwibG9nb3V0IiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJzZXRVc2VyIiwic2V0VXNlckRhdGEiLCJzZXRDaGlsZHJlbkRhdGEiLCJzZXRMb2FkaW5nIiwic2V0RXJyb3IiLCJzZXRJc0F1dGhlbnRpY2F0ZWQiLCJzZXRTdHVkZW50U2Vzc2lvbiIsInNldFVzZXJSb2xlIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInVwZGF0ZUN1cnJlbnRWaWV3Um9sZSIsInJvbGUiLCJjb25zb2xlIiwibG9nIiwic2V0SXRlbSIsImRhdGEiLCJzZXNzaW9uIiwic3RhdHVzIiwic2Vzc1JvbGUiLCJyb3V0ZXIiLCJwcmV2QXV0aFN0YXR1c1JlZiIsImdldEVmZmVjdGl2ZVJvbGUiLCJhY3R1YWxSb2xlIiwic2Vzc2lvblJvbGUiLCJ1c2VyRGF0YVJvbGUiLCJ1c2VyUm9sZVN0YXRlIiwiY3VycmVudFBhdGgiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsInBhdGhuYW1lIiwic3RhcnRzV2l0aCIsInN0b3JlZFVzZXJEYXRhIiwiSlNPTiIsInBhcnNlIiwic3RvcmVkQ2hpbGRyZW5EYXRhIiwidWlkIiwidG9rZW4iLCJnZXRJZFRva2VuIiwiZGVjb2RlZFRva2VuIiwiZ2V0SWRUb2tlblJlc3VsdCIsImN1c3RvbVJvbGUiLCJjbGFpbXMiLCJpc1BhcmVudFJvbGUiLCJpc09wZXJhdG9yUm9sZSIsImZpbmFsUm9sZSIsImVyciIsImRhc2hib2FyZFJlc3BvbnNlIiwiZmV0Y2giLCJkYXNoYm9hcmREYXRhIiwianNvbiIsIm9rIiwic3VjY2VzcyIsInN0cmluZ2lmeSIsImxlbmd0aCIsInBhcmVudFJlZiIsIm1hcCIsImNoaWxkIiwiaWQiLCJjaGlsZHJlblJlc3BvbnNlIiwiY2hpbGRyZW5SZXNEYXRhIiwibXNnIiwiRXJyb3IiLCJtZXNzYWdlIiwiY3VzdG9tVG9rZW4iLCJzdHVkZW50SWRIaW50IiwiaW5jbHVkZXMiLCJzaWduSW5Qcm9taXNlIiwidGltZW91dFByb21pc2UiLCJQcm9taXNlIiwiXyIsInJlamVjdCIsInNldFRpbWVvdXQiLCJ1c2VyQ3JlZGVudGlhbCIsInJhY2UiLCJsb2dnZWRJblVzZXIiLCJjdXJyZW50VXNlciIsInN0dWRlbnRJZCIsInJlbW92ZUl0ZW0iLCJpZFRva2VuIiwiY2xhaW1SZXNwb25zZSIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiZXJyb3JNZXNzYWdlIiwiZXJyb3JEYXRhIiwianNvbkVycm9yIiwiZXJyb3JUZXh0IiwidGV4dCIsInRleHRFcnJvciIsImUiLCJ1bnN1YnNjcmliZSIsInRpbWVzdGFtcCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsImVtYWlsIiwiaWRUb2tlblJlc3VsdCIsInNlc3Npb25Vc2VyIiwiZmV0Y2hVc2VyUm9sZSIsInJlc3BvbnNlIiwiY3JlZGVudGlhbHMiLCJyb2xlRGF0YSIsInN0b3JlZFZpZXdSb2xlIiwicHJlZmVycmVkVmlldyIsInN0b3JlZFJvbGUiLCJzdG9yZWRTdHVkZW50SWQiLCJ0ZW1wU3R1ZGVudFNlc3Npb24iLCJpc05leHRBdXRoQXV0aGVudGljYXRlZCIsImhhc1Nlc3Npb25XaXRoUm9sZSIsImZpcmViYXNlVXNlciIsInNlc3Npb25Vc2VyUm9sZSIsInJhd1Nlc3Npb24iLCJpc1JlYWxseUF1dGhlbnRpY2F0ZWQiLCJzdGF0dXNDaGVjayIsInNlc3Npb25Sb2xlQ2hlY2siLCJoYXNTZXNzaW9uQ2hlY2siLCJzZXNzaW9uVXNlclJvbGVDaGVjayIsInB1YmxpY1BhdGhzIiwic29tZSIsInAiLCJyZXBsYWNlIiwiZGVzdGluYXRpb24iLCJQcm92aWRlciIsInZhbHVlIiwiY2FjaGVSZXNwb25zZSIsIndhcm4iLCJjYWNoZUVycm9yIiwiY2xlYXIiLCJzZXNzaW9uU3RvcmFnZSIsImRhdGFiYXNlcyIsImluZGV4ZWREQiIsImZvckVhY2giLCJuYW1lIiwiZGVsZXRlRGF0YWJhc2UiLCJpZGJFcnJvciIsImNhY2hlTmFtZXMiLCJjYWNoZXMiLCJrZXlzIiwiYWxsIiwiY2FjaGVOYW1lIiwiZGVsZXRlIiwic3dFcnJvciIsInB1c2giLCJ1c2VBdXRoIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers/ClientToastWrapper.tsx":
/*!**************************************************!*\
  !*** ./src/app/providers/ClientToastWrapper.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientToastWrapper: () => (/* binding */ ClientToastWrapper),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ClientToastWrapper,useToast auto */ \n\nconst ToastContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createContext(null);\nfunction ClientToastWrapper({ children }) {\n    const [toasts, setToasts] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    const toast = (props)=>{\n        const id = props.id || Math.random().toString(36).substr(2, 9);\n        // Add to toasts array\n        setToasts((prev)=>[\n                ...prev,\n                {\n                    id,\n                    props\n                }\n            ]);\n        // Auto-dismiss after duration\n        const duration = props.duration || 5000;\n        setTimeout(()=>{\n            dismiss(id);\n        }, duration);\n        return {\n            id,\n            dismiss: ()=>dismiss(id)\n        };\n    };\n    const dismiss = (toastId)=>{\n        if (toastId) {\n            setToasts((prev)=>prev.filter((toast)=>toast.id !== toastId));\n        } else {\n            setToasts([]);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toast,\n            dismiss\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 right-4 z-50 space-y-2\",\n                children: toasts.map(({ id, props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n              bg-white border rounded-lg shadow-lg p-4 min-w-[300px] max-w-[400px] relative\n              ${props.variant === 'destructive' ? 'border-red-200 bg-red-50' : ''}\n              ${props.variant === 'success' ? 'border-green-200 bg-green-50' : ''}\n              ${props.variant === 'warning' ? 'border-yellow-200 bg-yellow-50' : ''}\n            `,\n                        children: [\n                            props.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-semibold mb-1\",\n                                children: props.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 29\n                            }, this),\n                            props.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: props.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 35\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>dismiss(id),\n                                className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-600 w-6 h-6 flex items-center justify-center\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n// Export the hook that components expect\nconst useToast = ()=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_1___default().useContext(ToastContext);\n    if (!context) {\n        console.error(\"useToast hook called outside of a ClientToastWrapper. Ensure ClientToastWrapper is placed correctly in your component tree (e.g., in client-providers.tsx).\");\n        throw new Error('useToast must be used within a ClientToastWrapper');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers/ClientToastWrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers/SessionProvider.tsx":
/*!***********************************************!*\
  !*** ./src/app/providers/SessionProvider.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children }) {\n    // Always wrap children in NextAuthSessionProvider\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\SessionProvider.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy9TZXNzaW9uUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUU2RTtBQUU5RCxTQUFTQSxnQkFBZ0IsRUFBRUUsUUFBUSxFQUFpQztJQUNqRixrREFBa0Q7SUFDbEQscUJBQ0UsOERBQUNELDREQUF1QkE7a0JBQ3JCQzs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXHNyY1xcYXBwXFxwcm92aWRlcnNcXFNlc3Npb25Qcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIGFzIE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XHJcbiAgLy8gQWx3YXlzIHdyYXAgY2hpbGRyZW4gaW4gTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXJcclxuICByZXR1cm4gKFxyXG4gICAgPE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICA8L05leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers/SessionProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers/ThemeProvider.tsx":
/*!*********************************************!*\
  !*** ./src/app/providers/ThemeProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// Theme provider with hydration-safe implementation\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light'); // Default theme\n    const [hasMounted, setHasMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Effect to run once on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            setHasMounted(true); // Mark as mounted\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Effect to apply theme once mounted\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (hasMounted) {\n                const savedTheme = localStorage.getItem('theme') || 'light';\n                setTheme(savedTheme); // Update state for potential context consumers\n                // Apply attributes directly to <html> tag\n                document.documentElement.className = savedTheme; // Set class for CSS targeting\n                document.documentElement.setAttribute('color-pick-mode', savedTheme);\n                document.documentElement.style.setProperty('color-scheme', savedTheme); // Set color-scheme style\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        hasMounted\n    ]); // Rerun if hasMounted changes (which is only once)\n    // Avoid rendering children until mounted to prevent mismatch\n    if (!hasMounted) {\n        // Render nothing or a placeholder/loader on the server and initial client render\n        // Returning children directly here could still cause mismatches if they depend on the theme\n        return null;\n    // Alternatively, render children but without theme-specific wrapper/context:\n    // return <>{children}</>;\n    }\n    // Once mounted, render children. The theme is applied via useEffect directly to documentElement.\n    // No need for a wrapper div with data-theme here.\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props), this.retry = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n        // Check if it's a ChunkLoadError\n        if (error.name === 'ChunkLoadError' || error.message.includes('Loading chunk')) {\n            console.log('ChunkLoadError detected, attempting to reload...');\n            // Reload the page to recover from chunk load errors\n            setTimeout(()=>{\n                window.location.reload();\n            }, 1000);\n        }\n    }\n    render() {\n        if (this.state.hasError) {\n            const FallbackComponent = this.props.fallback || DefaultErrorFallback;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                error: this.state.error,\n                retry: this.retry\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 45,\n                columnNumber: 14\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction DefaultErrorFallback({ error, retry }) {\n    const isChunkError = error?.name === 'ChunkLoadError' || error?.message.includes('Loading chunk');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-16 h-16 mx-auto\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.734-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                    children: isChunkError ? 'Loading Error' : 'Something went wrong'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-4\",\n                    children: isChunkError ? 'The application is updating. Please wait while we reload...' : 'An unexpected error occurred. Please try again.'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                isChunkError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: retry,\n                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors\",\n                    children: \"Try Again\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 11\n                }, this),\n                error && !isChunkError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mt-4 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"cursor-pointer text-sm text-gray-500\",\n                            children: \"Error Details\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 text-xs text-gray-600 overflow-auto\",\n                            children: error.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useSessionSimple.tsx":
/*!****************************************!*\
  !*** ./src/hooks/useSessionSimple.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authService */ \"(ssr)/./src/lib/authService.ts\");\n/* __next_internal_client_entry_do_not_use__ useSession,SessionContext auto */ \n // Import real auth service\n// Simplified session context for the frontend diagnostic fix\nconst SessionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nfunction useSession() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SessionContext);\n    if (context === undefined) {\n        // Instead of throwing an error immediately, return a fallback object\n        console.warn('useSession called outside of SessionProvider, returning fallback values');\n        return {\n            backendSessionId: null,\n            user: null,\n            setUserSession: ()=>{},\n            setBackendSessionId: ()=>{},\n            clearSession: ()=>{},\n            isReady: false,\n            isLoading: true,\n            getAuthHeaders: ()=>(0,_lib_authService__WEBPACK_IMPORTED_MODULE_1__.getAuthHeaders)(null),\n            userRole: null\n        };\n    }\n    return context;\n}\n// Export the context for providers to use\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useSessionSimple.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/authService.ts":
/*!********************************!*\
  !*** ./src/lib/authService.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CURRENT_SESSION_KEY: () => (/* binding */ CURRENT_SESSION_KEY),\n/* harmony export */   clearAuthData: () => (/* binding */ clearAuthData),\n/* harmony export */   findUserByUserId: () => (/* binding */ findUserByUserId),\n/* harmony export */   getAuthHeaders: () => (/* binding */ getAuthHeaders),\n/* harmony export */   getFreshAuthHeaders: () => (/* binding */ getFreshAuthHeaders),\n/* harmony export */   getUserRole: () => (/* binding */ getUserRole),\n/* harmony export */   getUserSession: () => (/* binding */ getUserSession),\n/* harmony export */   refreshAuthToken: () => (/* binding */ refreshAuthToken),\n/* harmony export */   saveUserSession: () => (/* binding */ saveUserSession),\n/* harmony export */   setupAuthListener: () => (/* binding */ setupAuthListener),\n/* harmony export */   setupAuthStateListener: () => (/* binding */ setupAuthStateListener),\n/* harmony export */   signInWithEmailAndPassword: () => (/* binding */ signInWithEmailAndPassword),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   syncAuthState: () => (/* binding */ syncAuthState)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n// lib/authService.ts\n/* __next_internal_client_entry_do_not_use__ CURRENT_SESSION_KEY,saveUserSession,getUserSession,clearAuthData,signInWithEmailAndPassword,signOut,setupAuthStateListener,setupAuthListener,getAuthHeaders,getFreshAuthHeaders,refreshAuthToken,findUserByUserId,getUserRole,syncAuthState auto */ \n\n\n// Constants\nconst SESSION_KEY = 'user_session';\nconst CURRENT_SESSION_KEY = 'current_session'; // Export this constant\nconst TOKEN_KEY = 'token';\n/**\r\n * Save user session to localStorage with consistent keys\r\n */ const saveUserSession = (session)=>{\n    if (!session || !session.uid) return;\n    try {\n        // Add timestamp before saving\n        const sessionToSave = {\n            ...session,\n            tokenTimestamp: Date.now()\n        };\n        // Save the full session object with timestamp\n        localStorage.setItem(SESSION_KEY, JSON.stringify(sessionToSave));\n        // CURRENT_SESSION_KEY should be set explicitly elsewhere when the *backend* session ID is known.\n        // Do not automatically set it to the Firebase UID here.\n        // localStorage.setItem(CURRENT_SESSION_KEY, session.uid); // Removed this line\n        localStorage.setItem(TOKEN_KEY, session.token); // Keep saving the token\n        console.log('Session object saved for UID:', session.uid);\n    } catch (error) {\n        console.error('Error saving user session:', error);\n    }\n};\n/**\r\n * Get the current user session from localStorage\r\n */ const getUserSession = ()=>{\n    try {\n        const sessionStr = localStorage.getItem(SESSION_KEY);\n        if (!sessionStr) return null;\n        return JSON.parse(sessionStr);\n    } catch (error) {\n        console.error('Failed to parse user session:', error);\n        return null;\n    }\n};\n/**\r\n * Clear all auth-related data from localStorage\r\n */ const clearAuthData = ()=>{\n    try {\n        localStorage.removeItem(SESSION_KEY);\n        localStorage.removeItem(TOKEN_KEY);\n        localStorage.removeItem(CURRENT_SESSION_KEY);\n        localStorage.removeItem('authMethod');\n        localStorage.removeItem('viewing_as_child');\n        localStorage.removeItem('parent_id');\n        localStorage.removeItem('parent_name');\n        // Remove additional role and session-related keys that may linger\n        localStorage.removeItem('user_role');\n        localStorage.removeItem('role');\n        localStorage.removeItem('calendly-store'); // In case our app inadvertently wrote it\n        localStorage.removeItem('calendly-internal-store');\n        localStorage.removeItem('user_name');\n        localStorage.removeItem('parentEnrollmentMessage');\n    } catch (error) {\n        console.error('Error clearing auth data:', error);\n    }\n};\n/**\r\n * Sign in with email and password\r\n */ const signInWithEmailAndPassword = async (email, password)=>{\n    // Clear any previous auth state first\n    await signOut();\n    console.log(\"Attempting email/password sign-in\");\n    try {\n        // Use Firebase's email/password auth\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, email, password);\n        const user = userCredential.user;\n        // Get fresh token with custom claims\n        const additionalClaims = {\n            student_id: localStorage.getItem('viewing_as_child') || undefined\n        };\n        const tokenResult = await user.getIdTokenResult(true);\n        const token = await _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser?.getIdToken(true, additionalClaims);\n        // Get user details from Firestore\n        const userDetails = await getUserDetailsFromFirestore(user);\n        // Create session\n        const userSession = {\n            uid: user.uid,\n            email: user.email,\n            name: user.displayName || userDetails?.name || null,\n            token: token,\n            role: userDetails?.role\n        };\n        // Save session\n        saveUserSession(userSession);\n        console.log(\"Authentication successful\");\n        return userSession;\n    } catch (error) {\n        console.error(\"Authentication error:\", error);\n        throw error;\n    }\n};\n/**\r\n * Sign out the current user\r\n */ const signOut = async ()=>{\n    try {\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth);\n        clearAuthData();\n        console.log(\"User signed out\");\n    } catch (error) {\n        console.error(\"Sign out error:\", error);\n        throw error;\n    }\n};\n/**\r\n * Set up a listener for auth state changes\r\n */ const setupAuthStateListener = (callback)=>{\n    return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, callback);\n};\n/**\r\n * Setup auth listener used by the session provider\r\n * This matches the signature expected by useSession\r\n */ const setupAuthListener = (setSession, setError, setIsLoading)=>{\n    return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, async (user)=>{\n        console.log(\"Auth state changed:\", user ? `User ${user.uid}` : \"No user\");\n        if (!user) {\n            setSession(null);\n            setIsLoading(false);\n            return;\n        }\n        try {\n            // Get fresh token for signed-in user\n            // Get custom claims including student_id if viewing as parent\n            const additionalClaims = {\n                student_id: localStorage.getItem('viewing_as_child') || undefined\n            };\n            const tokenResult = await user.getIdTokenResult(true);\n            const tokenString = await _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser?.getIdToken(true, additionalClaims);\n            if (!tokenString) {\n                throw new Error('Failed to get authentication token');\n            }\n            // Get user details from Firestore\n            const userDetails = await getUserDetailsFromFirestore(user);\n            // Create session object with token string\n            const userSession = {\n                uid: user.uid,\n                email: user.email || '',\n                name: user.displayName || userDetails?.name || '',\n                token: tokenString,\n                tokenResult,\n                role: userDetails?.role\n            };\n            // Set session and store backend session ID if available\n            setSession(userSession);\n            // If this is a new login response with sessionId, store it\n            const responseSessionId = user.sessionId;\n            if (responseSessionId) {\n                localStorage.setItem(CURRENT_SESSION_KEY, responseSessionId);\n            }\n        } catch (error) {\n            console.error(\"Error getting auth token:\", error);\n            setError(\"Failed to authenticate session\");\n            setSession(null);\n        } finally{\n            setIsLoading(false);\n        }\n    });\n};\n/**\r\n * Get auth headers for API requests\r\n * Accepts backendSessionId from context to avoid localStorage race conditions.\r\n */ const getAuthHeaders = (backendSessionIdFromContext)=>{\n    const headers = {\n        'Content-Type': 'application/json'\n    };\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    let currentToken = null;\n    let currentUid = null;\n    if (currentUser) {\n        currentUid = currentUser.uid;\n        // Attempt to get token from Firebase auth state first\n        currentToken = currentUser.stsTokenManager?.accessToken || null;\n    }\n    const storedSession = getUserSession();\n    // Prefer the ID passed from context, fall back to localStorage only if necessary (e.g., during initial load before context is ready)\n    const effectiveBackendSessionId = backendSessionIdFromContext || localStorage.getItem(CURRENT_SESSION_KEY);\n    // Use the effective backend session ID for the Session-ID header\n    if (effectiveBackendSessionId) {\n        headers['Session-ID'] = effectiveBackendSessionId;\n    } else {\n        // Fallback to UID only if backend session ID isn't available anywhere\n        const effectiveUid = currentUid || storedSession?.uid;\n        if (effectiveUid) {\n            console.warn(`Using UID (${effectiveUid}) as Session-ID header fallback. Backend session ID not found in context or localStorage ('${CURRENT_SESSION_KEY}').`);\n            headers['Session-ID'] = effectiveUid; // Still might be wrong, but it's the last resort\n        } else {\n            console.error(\"Cannot set Session-ID header: No backend session ID or user UID found.\");\n        }\n    }\n    // Prefer token from context's stored session if Firebase token is missing\n    const effectiveToken = currentToken || storedSession?.token;\n    if (effectiveToken) {\n        headers['Authorization'] = `Bearer ${effectiveToken}`;\n    } else {\n        console.warn(\"Authorization token not found in Firebase state or stored session. This may cause authentication errors.\");\n        // Instead of completely failing, let's try to get token from localStorage as last resort\n        const fallbackToken = localStorage.getItem('token');\n        if (fallbackToken) {\n            console.warn(\"Using fallback token from localStorage\");\n            headers['Authorization'] = `Bearer ${fallbackToken}`;\n        } else {\n            console.error(\"No authentication token available from any source.\");\n        }\n    }\n    // Get role from stored session if available\n    const effectiveRole = storedSession?.role || 'student'; // Default role if not found\n    headers['X-User-Role'] = effectiveRole;\n    // CRITICAL FIX: Add testing mode header when no valid authentication is available\n    if (!effectiveToken && !effectiveBackendSessionId) {\n        console.warn(\"No authentication available, setting testing mode header\");\n        headers['X-Testing-Mode'] = 'true';\n        // For testing purposes, set a default student ID if none available\n        if (!headers['Session-ID']) {\n            headers['Session-ID'] = 'andrea_ugono_33305'; // Default test student ID\n        }\n    }\n    console.log(\"Generated auth headers:\", {\n        hasAuth: !!headers['Authorization'],\n        hasSessionId: !!headers['Session-ID'],\n        role: headers['X-User-Role'],\n        testingMode: headers['X-Testing-Mode']\n    });\n    return headers;\n    // This allows backend to generate console logs for lesson interactions during development\n    if (!effectiveToken || effectiveToken === 'undefined' || effectiveToken === 'null') {\n        console.warn(\"No valid authentication token found - enabling testing mode for backend logging\");\n        headers['X-Testing-Mode'] = 'true';\n    }\n    return headers;\n};\n/**\r\n * Get fresh auth headers with token refresh\r\n * Accepts backendSessionId from context.\r\n */ const getFreshAuthHeaders = async (backendSessionIdFromContext)=>{\n    const headers = {\n        'Content-Type': 'application/json'\n    };\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    if (currentUser) {\n        try {\n            const token = await currentUser.getIdToken(true); // Force refresh\n            headers['Authorization'] = `Bearer ${token}`;\n            // Use the effective backend session ID for the Session-ID header\n            const effectiveBackendSessionId = backendSessionIdFromContext || localStorage.getItem(CURRENT_SESSION_KEY);\n            if (effectiveBackendSessionId) {\n                headers['Session-ID'] = effectiveBackendSessionId;\n            } else {\n                // Fallback to UID only if backend session ID isn't available anywhere\n                console.warn(`Using UID (${currentUser.uid}) as Session-ID header fallback during fresh token request. Backend session ID not found.`);\n                headers['Session-ID'] = currentUser.uid; // Last resort\n            }\n            const storedSession = getUserSession();\n            headers['X-User-Role'] = storedSession?.role || 'student'; // Default role\n        } catch (error) {\n            console.error(\"Error getting fresh token:\", error);\n            // Fallback to non-fresh headers if refresh fails\n            return getAuthHeaders(backendSessionIdFromContext);\n        }\n    } else {\n        // If no current user, return standard (likely unauthenticated) headers with testing mode\n        const fallbackHeaders = getAuthHeaders(backendSessionIdFromContext);\n        fallbackHeaders['X-Testing-Mode'] = 'true';\n        console.warn(\"No current user found - enabling testing mode for backend logging\");\n        return fallbackHeaders;\n    }\n    return headers;\n};\n/**\r\n * Refresh the auth token\r\n */ const refreshAuthToken = async ()=>{\n    try {\n        const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n        if (!currentUser) {\n            console.error(\"No current user found for token refresh\");\n            return null;\n        }\n        // Force refresh the token\n        const newToken = await currentUser.getIdToken(true);\n        // Update the stored session with new token\n        const storedSession = getUserSession();\n        if (storedSession) {\n            const updatedSession = {\n                ...storedSession,\n                token: newToken\n            };\n            saveUserSession(updatedSession);\n        }\n        return newToken;\n    } catch (error) {\n        console.error(\"Failed to refresh authentication token:\", error);\n        return null;\n    }\n};\n/**\r\n * Get user details from Firestore\r\n */ async function getUserDetailsFromFirestore(user) {\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'users', user.uid);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            return {\n                name: data.name,\n                role: data.role,\n                children: data.children || [],\n                parents: data.parents || []\n            };\n        }\n        // Fallback to check parents collection if not found in users\n        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'parents', user.uid);\n        const parentDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(parentRef);\n        if (parentDoc.exists()) {\n            const data = parentDoc.data();\n            return {\n                name: data.name,\n                role: 'parent',\n                children: data.children || []\n            };\n        }\n        // Fallback to check students collection\n        const studentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'students', user.uid);\n        const studentDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(studentRef);\n        if (studentDoc.exists()) {\n            const data = studentDoc.data();\n            return {\n                name: data.name,\n                role: 'student',\n                parents: data.parents || []\n            };\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error fetching user details:\", error);\n        return null;\n    }\n}\n/**\r\n * Find user by userId (for child accounts)\r\n */ const findUserByUserId = async (userId)=>{\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(db, 'users');\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(usersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        if (querySnapshot.empty) {\n            return null;\n        }\n        return querySnapshot.docs[0].data().email;\n    } catch (error) {\n        console.error(\"Error finding user by userId:\", error);\n        return null;\n    }\n};\n/**\r\n * Get user role from Firestore\r\n */ const getUserRole = async (uid)=>{\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'users', uid);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(userRef);\n        if (userDoc.exists() && userDoc.data().role) {\n            return userDoc.data().role;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error getting user role:\", error);\n        return null;\n    }\n};\n/**\r\n * Sync Firebase auth state with local storage\r\n * This is crucial to fix the state mismatch issues\r\n */ const syncAuthState = async ()=>{\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    const storedSession = getUserSession();\n    // Case 1: Firebase has user but localStorage doesn't\n    if (currentUser && (!storedSession || storedSession.uid !== currentUser.uid)) {\n        console.log(\"Syncing: Firebase has user but localStorage doesn't match\");\n        const token = await currentUser.getIdToken(true);\n        const userDetails = await getUserDetailsFromFirestore(currentUser);\n        const userSession = {\n            uid: currentUser.uid,\n            email: currentUser.email,\n            name: currentUser.displayName || userDetails?.name || null,\n            token: token,\n            role: userDetails?.role\n        };\n        saveUserSession(userSession);\n        return userSession;\n    }\n    // Case 2: Firebase has no user but localStorage does\n    if (!currentUser && storedSession) {\n        console.log(\"Syncing: Firebase has no user but localStorage does\");\n        clearAuthData();\n        return null;\n    }\n    // Case 3: Both have matching user, check if token needs refresh\n    if (currentUser && storedSession && currentUser.uid === storedSession.uid) {\n        console.log(\"Syncing: Both have matching user\");\n        // Token is older than 30 minutes, refresh it\n        const tokenDate = new Date(storedSession.tokenTimestamp || 0);\n        const now = new Date();\n        const diffMinutes = (now.getTime() - tokenDate.getTime()) / (1000 * 60);\n        if (diffMinutes > 30) {\n            console.log(\"Token is older than 30 minutes, refreshing\");\n            await refreshAuthToken();\n        }\n    }\n    return storedSession;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/authService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   app: () => (/* binding */ app),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n// lib/firebase.ts\n/* __next_internal_client_entry_do_not_use__ app,auth,db,storage auto */ \n\n\n\n// Default Firebase configuration for development\nconst devConfig = {\n    apiKey: \"AIzaSyDWVM8PvcWD4nAkpsI7FuDKCvpp_PEnPlU\",\n    authDomain: \"solynta-academy.firebaseapp.com\",\n    projectId: \"solynta-academy\",\n    storageBucket: \"solynta-academy.firebasestorage.app\",\n    messagingSenderId: \"914922463191\",\n    appId: \"1:914922463191:web:b6e9c737dba77a26643592\",\n    measurementId: \"G-ZVC7R06Y33\"\n};\n// Firebase configuration - try environment variables first, then fallback to dev config\nconst firebaseConfig = {\n    apiKey: \"AIzaSyDWVM8PvcWD4nAkpsI7FuDKCvpp_PEnPlU\" || 0,\n    authDomain: \"solynta-academy.firebaseapp.com\" || 0,\n    projectId: \"solynta-academy\" || 0,\n    storageBucket: \"solynta-academy.firebasestorage.app\" || 0,\n    messagingSenderId: \"914922463191\" || 0,\n    appId: \"1:914922463191:web:b6e9c737dba77a26643592\" || 0,\n    measurementId: \"G-ZVC7R06Y33\" || 0\n};\nconsole.log('Using Firebase config with project ID:', firebaseConfig.projectId);\n// Initialize Firebase app (Singleton pattern)\nconst app = !(0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig) : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApp)();\n// Initialize services - these will be initialized client-side\nlet auth;\nlet db;\nlet storage;\n// Check if running in a browser environment\nif (false) {} else {\n    // Provide non-functional placeholders for SSR/server environments\n    // This prevents errors during import but these services won't work server-side\n    // Assigning {} as Type might cause issues if methods are called server-side.\n    // A more robust approach might involve providing mock implementations or\n    // ensuring these exports are only used in client components.\n    auth = {};\n    db = {};\n    storage = {};\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@grpc/grpc-js":
/*!********************************!*\
  !*** external "@grpc/grpc-js" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@grpc/grpc-js");

/***/ }),

/***/ "@grpc/proto-loader":
/*!*************************************!*\
  !*** external "@grpc/proto-loader" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@grpc/proto-loader");

/***/ }),

/***/ "@opentelemetry/api":
/*!*************************************!*\
  !*** external "@opentelemetry/api" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@opentelemetry/api");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@firebase","vendor-chunks/framer-motion","vendor-chunks/@react-aria","vendor-chunks/tslib","vendor-chunks/idb","vendor-chunks/motion-utils","vendor-chunks/next-themes","vendor-chunks/@nextui-org","vendor-chunks/firebase","vendor-chunks/lucide-react","vendor-chunks/motion-dom"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();